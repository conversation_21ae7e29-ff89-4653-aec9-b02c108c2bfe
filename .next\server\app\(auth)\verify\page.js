(()=>{var a={};a.id=6474,a.ids=[6474],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1016:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(auth)\\\\verify\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(auth)\\verify\\page.tsx","default")},2578:(a,b,c)=>{"use strict";c.d(b,{A:()=>q});var d=c(60687),e=c(43210),f=c(16189),g=c(25472),h=c(27605),i=c(558),j=c(80942),k=c(89667),l=c(23492),m=c(5544),n=c(51714),o=c(93853),p=c(78314);function q({userId:a,typeVerify:b}){let[c,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(!1),t=(0,f.useRouter)(),{setUser:u}=(0,p.U)(),v=(0,h.mN)({resolver:(0,i.u)(m.ZZ),defaultValues:{code:"",userId:a,deviceId:""}});async function w(a){if(r)return;s(!0);let c=await (0,g.I)();try{let d=null;"authapp"===b?d=await l.A.VerifyAppCode({...a,deviceId:c}):"authmail"===b&&(d=await l.A.VerifyCode({...a,deviceId:c})),d&&(await l.A.auth({sessionToken:d.payload.token,expiresAt:d.payload.expiresAt,user:d.payload.user}),o.oR.success("Đăng nhập th\xe0nh c\xf4ng!"),u(d.payload.user),t.push("/dashboard"),t.refresh())}catch(a){403===a.status&&q(a.payload.code),o.oR.error(a.payload.message)}finally{s(!1)}}return(0,d.jsx)(j.lV,{...v,children:(0,d.jsxs)("form",{className:"mx-auto w-full mb-5",onSubmit:v.handleSubmit(w),noValidate:!0,children:[(0,d.jsx)(j.zB,{control:v.control,name:"code",render:({field:a})=>(0,d.jsxs)(j.eI,{children:[(0,d.jsx)(j.MJ,{children:(0,d.jsx)(k.p,{placeholder:"Code",type:"text",...a})}),(0,d.jsx)(j.C5,{})]})}),(0,d.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:c}),(0,d.jsxs)("button",{disabled:!!r,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:[r?(0,d.jsx)(n.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})]})})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5544:(a,b,c)=>{"use strict";c.d(b,{Ap:()=>h,ZZ:()=>i,aU:()=>e,ab:()=>g,iV:()=>f});var d=c(41597);let e=d.Ay.object({username:d.Ay.string().trim().min(2).max(256),email:d.Ay.string().email(),password:d.Ay.string().min(6).max(100),confirmPassword:d.Ay.string().min(6).max(100)}).strict().superRefine(({confirmPassword:a,password:b},c)=>{a!==b&&c.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})});d.Ay.object({token:d.Ay.string(),user:d.Ay.object({_id:d.Ay.number(),username:d.Ay.string(),email:d.Ay.string(),rule:d.Ay.string()}),message:d.Ay.string()});let f=d.Ay.object({email:d.Ay.string().email(),password:d.Ay.string().min(6).max(100),deviceId:d.Ay.string()}).strict(),g=d.Ay.object({email:d.Ay.string().email()}).strict(),h=d.Ay.object({email:d.Ay.string().email(),code:d.Ay.string(),password:d.Ay.string().min(6).max(100)}).strict();d.Ay.object({}).strict();let i=d.Ay.object({code:d.Ay.string().min(6),userId:d.Ay.string(),deviceId:d.Ay.string()}).strict();d.Ay.object({userId:d.Ay.string()}).strict()},7694:(a,b,c)=>{Promise.resolve().then(c.bind(c,79246))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16592:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(auth)",{children:["verify",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1016)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(auth)\\verify\\page.tsx"]}]},{}]},{forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(auth)\\verify\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(auth)/verify/page",pathname:"/verify",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(auth)/verify/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25472:(a,b,c)=>{"use strict";c.d(b,{I:()=>e});var d=c(2162);let e=async()=>{let a=await d.Ay.load();return(await a.get()).visitorId}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79246:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(60687),e=c(43210),f=c(16189),g=c(2578);function h(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,d.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:"Loading..."})}),children:(0,d.jsx)(i,{})})}function i(){(0,f.useSearchParams)().get("id");let[a,b]=(0,e.useState)(""),[c,h]=(0,e.useState)("");return c?(0,d.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,d.jsx)("p",{children:c})}):a?(0,d.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,d.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,d.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,d.jsx)("h1",{className:"text-2xl text-center mb-4",children:"X\xe1c nhận bước 2"}),(0,d.jsx)("span",{className:"text-center block mb-4",children:"Bạn cần kiểm tra Email đ\xe3 khai b\xe1o nhận m\xe3 x\xe1c thực lớp 2, lấy m\xe3 v\xe0 nhập v\xe0o \xf4 dưới đ\xe2y."}),(0,d.jsx)(g.A,{userId:a,typeVerify:"authmail"})]})})}):(0,d.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,d.jsx)("p",{children:"Loading user information..."})})}c(23492)},79428:a=>{"use strict";a.exports=require("buffer")},80942:(a,b,c)=>{"use strict";c.d(b,{lV:()=>l,MJ:()=>s,zB:()=>n,eI:()=>q,lR:()=>r,C5:()=>t});var d=c(60687),e=c(43210),f=c(81391),g=c(27605),h=c(4780),i=c(69467);let j=(0,c(24224).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(i.b,{ref:c,className:(0,h.cn)(j(),a),...b}));k.displayName=i.b.displayName;let l=g.Op,m=e.createContext({}),n=({...a})=>(0,d.jsx)(m.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),o=()=>{let a=e.useContext(m),b=e.useContext(p),{getFieldState:c,formState:d}=(0,g.xW)(),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},p=e.createContext({}),q=e.forwardRef(({className:a,...b},c)=>{let f=e.useId();return(0,d.jsx)(p.Provider,{value:{id:f},children:(0,d.jsx)("div",{ref:c,className:(0,h.cn)("mb-4",a),...b})})});q.displayName="FormItem";let r=e.forwardRef(({className:a,...b},c)=>{let{error:e,formItemId:f}=o();return(0,d.jsx)(k,{ref:c,className:(0,h.cn)(e&&"text-destructive",a),htmlFor:f,...b})});r.displayName="FormLabel";let s=e.forwardRef(({...a},b)=>{let{error:c,formItemId:e,formDescriptionId:g,formMessageId:h}=o();return(0,d.jsx)(f.DX,{ref:b,id:e,"aria-describedby":c?`${g} ${h}`:`${g}`,"aria-invalid":!!c,...a})});s.displayName="FormControl",e.forwardRef(({className:a,...b},c)=>{let{formDescriptionId:e}=o();return(0,d.jsx)("p",{ref:c,id:e,className:(0,h.cn)("text-[0.8rem] text-muted-foreground",a),...b})}).displayName="FormDescription";let t=e.forwardRef(({className:a,children:b,...c},e)=>{let{error:f,formMessageId:g}=o(),i=f?String(f?.message):b;return i?(0,d.jsx)("p",{ref:e,id:g,className:(0,h.cn)("text-[0.8rem] font-medium text-red-600",a),...c,children:i}):null});t.displayName="FormMessage"},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89542:(a,b,c)=>{Promise.resolve().then(c.bind(c,1016))},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>i});var d=c(60687),e=c(4780),f=c(85444),g=c(22960),h=c(43210);let i=c.n(h)().forwardRef(({className:a,type:b,...c},i)=>{let[j,k]=(0,h.useState)(!1);return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"relative w-full",children:[(0,d.jsx)("input",{type:"password"===b&&j?"text":b,autoComplete:"password"===b?"new-password":"",className:(0,e.cn)("input input-bordered w-full rounded-md",a),ref:i,...c}),"password"===b&&(j?(0,d.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}):(0,d.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}))]})})});i.displayName="Input"}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,2415,2493,9377],()=>b(b.s=16592));module.exports=c})();