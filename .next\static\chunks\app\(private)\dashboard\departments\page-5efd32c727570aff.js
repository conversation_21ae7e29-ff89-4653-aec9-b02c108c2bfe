(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5740],{3136:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(27937);let n={createDepartment:(e,s)=>a.Ay.post("/api/departments",e,{headers:{Authorization:"Bearer ".concat(s)}}),getDepartments:(e,s)=>a.Ay.post("/api/departments/list",e,{headers:{Authorization:"Bearer ".concat(s)}}),getDepartmentById:(e,s)=>a.Ay.get("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(s)}}),updateDepartment:(e,s,t)=>a.Ay.put("/api/departments/".concat(e),s,{headers:{Authorization:"Bearer ".concat(t)}}),deleteDepartment:(e,s)=>a.Ay.delete("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(s)}}),addMemberToDepartment:(e,s,t)=>a.Ay.post("/api/departments/".concat(e,"/members"),s,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartmentMembers:(e,s,t)=>a.Ay.post("/api/departments/".concat(e,"/members/list"),s,{headers:{Authorization:"Bearer ".concat(t)}}),updateMemberPermissions:(e,s,t,n)=>a.Ay.put("/api/departments/".concat(e,"/members/").concat(s),t,{headers:{Authorization:"Bearer ".concat(n)}}),removeMemberFromDepartment:(e,s,t)=>a.Ay.delete("/api/departments/".concat(e,"/members/").concat(s),{headers:{Authorization:"Bearer ".concat(t)}}),getAvailablePermissions:e=>a.Ay.get("/api/departments/permissions",{headers:{Authorization:"Bearer ".concat(e)}})}},3892:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(95155),n=t(12115),r=t(36268),i=t(11032),l=t(3136),c=t(38543),d=t(35695),o=t(18579),h=t(52814),m=t(21379),g=t(8509),x=t(46114),u=t(68661),p=t(38497);function y(){let[e,s]=(0,n.useState)([]),[t,y]=(0,n.useState)(!0),[b,j]=(0,n.useState)(0),[v,f]=(0,n.useState)(1),[N,w]=(0,n.useState)(""),k=(0,d.useRouter)(),{isAdmin:_}=(0,p.S)(),C=[{accessorKey:"name",header:"T\xean ph\xf2ng ban",cell:e=>{let{row:s}=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s.original.name}),s.original.description&&(0,a.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:s.original.description})]})}},{accessorKey:"manager",header:"Quản l\xfd",cell:e=>{let{row:s}=e;return(0,a.jsx)("div",{children:s.original.manager?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s.original.manager.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:s.original.manager.email})]}):(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 quản l\xfd"})})}},{accessorKey:"memberCount",header:"Số th\xe0nh vi\xean",cell:e=>{let{row:s}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{size:16,className:"text-gray-500"}),(0,a.jsx)("span",{className:"font-medium",children:s.original.memberCount})]})}},{accessorKey:"defaultPermissions",header:"Quyền mặc định",cell:e=>{let{row:s}=e;return(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:s.original.defaultPermissions.length>0?(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(h.Ex,{variant:"secondary",className:"text-xs",children:[s.original.defaultPermissions.length," quyền"]})}):(0,a.jsx)("span",{className:"text-gray-400 italic text-sm",children:"Kh\xf4ng c\xf3 quyền"})})}},{accessorKey:"isActive",header:"Trạng th\xe1i",cell:e=>{let{row:s}=e;return(0,a.jsx)(h.Ex,{variant:s.original.isActive?"success":"danger",children:s.original.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})}},{id:"actions",header:"Thao t\xe1c",cell:e=>{let{row:s}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>k.push("/dashboard/departments/".concat(s.original._id)),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,a.jsx)(m.A,{size:16})}),_&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>k.push("/dashboard/departments/".concat(s.original._id,"/edit")),className:"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,a.jsx)(g.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>S(s.original._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,a.jsx)(x.A,{size:16})})]})]})}}],A=(0,r.N4)({data:e,columns:C,getCoreRowModel:(0,i.HT)()}),T=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{y(!0);let a=localStorage.getItem("sessionToken")||"",n=await l.A.getDepartments({page:e,perPage:10,search:t},a);n.payload.success?(s(n.payload.departments),j(n.payload.total)):c.oR.error("Kh\xf4ng thể tải danh s\xe1ch ph\xf2ng ban")}catch(e){console.error("Error fetching departments:",e),c.oR.error("Lỗi khi tải danh s\xe1ch ph\xf2ng ban")}finally{y(!1)}},S=async s=>{let t=e.find(e=>e._id===s),a=(null==t?void 0:t.memberCount)||0,n="Bạn c\xf3 chắc chắn muốn x\xf3a ph\xf2ng ban n\xe0y?";if(a>0&&(n="⚠️ CẢNH B\xc1O: Ph\xf2ng ban n\xe0y c\xf3 ".concat(a," th\xe0nh vi\xean!\n\n")+"Khi x\xf3a ph\xf2ng ban:\n"+"• Tất cả ".concat(a," th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban\n")+"• Họ sẽ trở th\xe0nh người d\xf9ng thường (kh\xf4ng thuộc ph\xf2ng ban n\xe0o)\n• C\xe1c quyền li\xean quan đến ph\xf2ng ban sẽ bị mất\n\nBạn c\xf3 chắc chắn muốn tiếp tục?"),confirm(n))try{let e=localStorage.getItem("sessionToken")||"",t=await l.A.deleteDepartment(s,e);t.payload.success?(t.payload.affectedMembers>0?c.oR.success("X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng! ".concat(t.payload.affectedMembers," th\xe0nh vi\xean đ\xe3 được chuyển về trạng th\xe1i kh\xf4ng thuộc ph\xf2ng ban."),{autoClose:5e3}):c.oR.success("X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng"),T(v,N)):c.oR.error(t.payload.message||"Kh\xf4ng thể x\xf3a ph\xf2ng ban")}catch(e){console.error("Error deleting department:",e),c.oR.error("Lỗi khi x\xf3a ph\xf2ng ban")}};return((0,n.useEffect)(()=>{T(v,N)},[v]),t)?(0,a.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tổng cộng ",b," ph\xf2ng ban"]})]}),_&&(0,a.jsxs)("button",{onClick:()=>k.push("/dashboard/departments/add"),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(u.A,{size:16}),"Th\xeam ph\xf2ng ban"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(1),T(1,N)},className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",placeholder:"T\xecm kiếm theo t\xean, m\xe3 hoặc m\xf4 tả...",value:N,onChange:e=>w(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"T\xecm kiếm"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:A.getHeaderGroups().map(e=>(0,a.jsx)("tr",{children:e.headers.map(e=>(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.isPlaceholder?null:(0,r.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.getRowModel().rows.map(e=>(0,a.jsx)("tr",{className:"hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Kh\xf4ng c\xf3 ph\xf2ng ban n\xe0o"})]}),b>10&&(0,a.jsx)(o.A,{currentPage:v,totalPages:Math.ceil(b/10),onPageChange:f})]})}var b=t(23348),j=t(21953),v=t(59698),f=t(17691),N=t(77713),w=t(11725);function k(e){let{isOpen:s,onClose:t,departmentId:r,onMemberAdded:i}=e,[d,o]=(0,n.useState)([]),[h,m]=(0,n.useState)(!1),[g,x]=(0,n.useState)(""),[u,p]=(0,n.useState)(""),[y,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{s&&v()},[s]);let v=async()=>{try{m(!0);let e=localStorage.getItem("sessionToken")||"",s=await w.A.getAllUsers({page:1,perPage:100,search:g},e);if(s.payload.success){let e=s.payload.users.filter(e=>!e.department&&"admin"!==e.rule);o(e)}}catch(e){console.error("Error fetching users:",e),c.oR.error("Lỗi khi tải danh s\xe1ch người d\xf9ng")}finally{m(!1)}},k=async()=>{if(!u)return void c.oR.error("Vui l\xf2ng chọn người d\xf9ng");try{b(!0);let e=localStorage.getItem("sessionToken")||"",s=await l.A.addMemberToDepartment(r,{userId:u,departmentRole:"member",permissions:[]},e);s.payload.success?(c.oR.success("Đ\xe3 th\xeam th\xe0nh vi\xean th\xe0nh c\xf4ng"),i(),t(),p("")):c.oR.error(s.payload.message||"Kh\xf4ng thể th\xeam th\xe0nh vi\xean")}catch(e){console.error("Error adding member:",e),c.oR.error("Lỗi khi th\xeam th\xe0nh vi\xean")}finally{b(!1)}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Th\xeam th\xe0nh vi\xean mới"}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,a.jsx)(f.A,{size:20})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"T\xecm kiếm người d\xf9ng...",value:g,onChange:e=>x(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"}),(0,a.jsx)(N.A,{size:16,className:"absolute left-3 top-3 text-gray-400"})]}),(0,a.jsx)("button",{onClick:()=>{v()},className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"T\xecm"})]}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:h?(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"})}):d.length>0?d.map(e=>(0,a.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer bg-white",children:[(0,a.jsx)("input",{type:"radio",name:"selectedUser",value:e._id,checked:u===e._id,onChange:e=>p(e.target.value),className:"mr-3 text-gray-900"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.email})]})]},e._id)):(0,a.jsx)("div",{className:"text-center py-4 text-gray-600",children:"Kh\xf4ng c\xf3 người d\xf9ng khả dụng"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,a.jsxs)("button",{onClick:k,disabled:!u||y,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[y?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,a.jsx)(j.A,{size:16}),y?"Đang th\xeam...":"Th\xeam th\xe0nh vi\xean"]})]})]})}):null}var _=t(99310);let C={user_view:"Xem người d\xf9ng",user_add:"Th\xeam người d\xf9ng",user_edit:"Sửa người d\xf9ng",user_delete:"X\xf3a người d\xf9ng",user_import:"Nhập người d\xf9ng",user_export:"Xuất người d\xf9ng",file_view:"Xem file",file_upload:"Tải l\xean file",file_delete:"X\xf3a file",file_download:"Tải xuống file",court_case_view:"Xem vụ việc",court_case_create:"Tạo vụ việc",court_case_edit:"Sửa vụ việc",court_case_delete:"X\xf3a vụ việc",court_case_export:"Xuất vụ việc",court_case_import:"Nhập vụ việc",court_case_stats:"Thống k\xea vụ việc",department_view:"Xem ph\xf2ng ban",department_create:"Tạo ph\xf2ng ban",department_edit:"Sửa ph\xf2ng ban",department_delete:"X\xf3a ph\xf2ng ban",department_member_manage:"Quản l\xfd th\xe0nh vi\xean",system_settings_view:"Xem c\xe0i đặt hệ thống",system_settings_edit:"Sửa c\xe0i đặt hệ thống",system_logs_view:"Xem nhật k\xfd hệ thống",system_admin_full_access:"To\xe0n quyền quản trị",system_departments_manage:"Quản l\xfd ph\xf2ng ban hệ thống",system_users_manage:"Quản l\xfd người d\xf9ng hệ thống",system_settings_manage:"Quản l\xfd c\xe0i đặt hệ thống",analytics_view:"Xem thống k\xea",analytics_export:"Xuất thống k\xea",permissions_manage:"Quản l\xfd quyền",admin:"Quản trị vi\xean"},A=e=>C[e]||e;function T(e){let{isOpen:s,onClose:t,departmentId:r,departmentPermissions:i,onUserCreated:d}=e,{user:o}=(0,b.U)(),[h,m]=(0,n.useState)({username:"",email:"",password:"",phonenumber:"",departmentRole:"member"}),[g,x]=(0,n.useState)([]),[u,p]=(0,n.useState)(!1),[y,N]=(0,n.useState)(!1),[w,k]=(0,n.useState)(!0);(0,n.useEffect)(()=>{s&&(m({username:"",email:"",password:"",phonenumber:"",departmentRole:"member"}),x([]),k(!0),p(!1))},[s]);let C=e=>{let{name:s,value:t}=e.target;m(e=>({...e,[s]:t}))},T=async()=>{if(!h.username.trim())return void c.oR.error("Vui l\xf2ng nhập t\xean người d\xf9ng");if(!h.email.trim())return void c.oR.error("Vui l\xf2ng nhập email");if(!w&&!h.password.trim())return void c.oR.error("Vui l\xf2ng nhập mật khẩu");try{N(!0);let e=localStorage.getItem("sessionToken")||"",s=w?(()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",s="";for(let t=0;t<12;t++)s+=e.charAt(Math.floor(Math.random()*e.length));return s})():h.password,a=await l.A.addMemberToDepartment(r,{username:h.username,email:h.email,password:s,phonenumber:h.phonenumber,departmentRole:h.departmentRole,permissions:g},e);a.payload.success?(c.oR.success("Đ\xe3 tạo t\xe0i khoản th\xe0nh c\xf4ng! ".concat(w?"Mật khẩu: ".concat(s):"")),d(),t()):c.oR.error(a.payload.message||"Kh\xf4ng thể tạo t\xe0i khoản")}catch(e){console.error("Error creating user:",e),c.oR.error("Lỗi khi tạo t\xe0i khoản")}finally{N(!1)}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo t\xe0i khoản mới"}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,a.jsx)(f.A,{size:20})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Th\xf4ng tin cơ bản"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xean người d\xf9ng *"}),(0,a.jsx)("input",{type:"text",name:"username",value:h.username,onChange:C,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập t\xean người d\xf9ng"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,a.jsx)("input",{type:"email",name:"email",value:h.email,onChange:C,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Số điện thoại"}),(0,a.jsx)("input",{type:"tel",name:"phonenumber",value:h.phonenumber,onChange:C,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập số điện thoại"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,a.jsxs)("select",{name:"departmentRole",value:h.departmentRole,onChange:C,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"member",children:"Th\xe0nh vi\xean"}),(0,a.jsx)("option",{value:"deputy",children:"Ph\xf3 ph\xf2ng"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Mật khẩu"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:w,onChange:e=>k(e.target.checked),className:"mr-2 text-gray-900"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Tự động tạo mật khẩu"})]}),!w&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:u?"text":"password",name:"password",value:h.password,onChange:C,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập mật khẩu"}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!u),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:u?(0,a.jsx)(_.A,{size:16}):(0,a.jsx)(v.A,{size:16})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Quyền truy cập"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Chọn c\xe1c quyền từ danh s\xe1ch quyền của ph\xf2ng ban"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-white",children:i.map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.includes(e),onChange:s=>{s.target.checked?x(s=>[...s,e]):x(s=>s.filter(s=>s!==e))},className:"mr-2 text-gray-900"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:A(e)})]},e))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,a.jsxs)("button",{onClick:T,disabled:y,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[y?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,a.jsx)(j.A,{size:16}),y?"Đang tạo...":"Tạo t\xe0i khoản"]})]})]})}):null}var S=t(64332),R=t(11080);function z(e){var s,t;let{isOpen:r,onClose:i,member:d,departmentId:o,departmentPermissions:h,onPermissionsUpdated:m}=e,[g,x]=(0,n.useState)([]),[u,p]=(0,n.useState)("member"),[y,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{r&&d&&(x(d.permissions||[]),p(d.departmentRole||"member"))},[r,d]);let j=async()=>{if(d)try{b(!0);let e=localStorage.getItem("sessionToken")||"",s=await l.A.updateMemberPermissions(o,d._id,{permissions:g,departmentRole:u},e);s.payload.success?(c.oR.success("Đ\xe3 cập nhật quyền th\xe0nh c\xf4ng"),m(),i()):c.oR.error(s.payload.message||"Kh\xf4ng thể cập nhật quyền")}catch(e){console.error("Error updating permissions:",e),c.oR.error("Lỗi khi cập nhật quyền")}finally{b(!1)}};return r&&d?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(S.A,{className:"text-blue-600",size:20}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa quyền th\xe0nh vi\xean"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[d.username," (",d.email,")"]})]})]}),(0,a.jsx)("button",{onClick:i,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,a.jsx)(f.A,{size:20})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,a.jsxs)("select",{value:u,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"member",children:"Th\xe0nh vi\xean"}),(0,a.jsx)("option",{value:"deputy",children:"Ph\xf3 ph\xf2ng"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Quyền truy cập"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>x(h),className:"text-sm text-blue-600 hover:text-blue-700",children:"Chọn tất cả"}),(0,a.jsx)("span",{className:"text-gray-300",children:"|"}),(0,a.jsx)("button",{onClick:()=>x([]),className:"text-sm text-red-600 hover:text-red-700",children:"Bỏ chọn tất cả"})]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Chọn c\xe1c quyền từ danh s\xe1ch quyền của ph\xf2ng ban (",h.length," quyền c\xf3 sẵn)"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-white",children:h.map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.includes(e),onChange:s=>{s.target.checked?x(s=>[...s,e]):x(s=>s.filter(s=>s!==e))},className:"mr-2 text-gray-900"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:A(e)})]},e))}),0===h.length&&(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Ph\xf2ng ban chưa c\xf3 quyền n\xe0o được cấu h\xecnh"})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,a.jsx)("h5",{className:"font-medium text-gray-900",children:"T\xf3m tắt thay đổi"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-gray-900 mb-2",children:["Quyền hiện tại (",(null==(s=d.permissions)?void 0:s.length)||0,"):"]}),(0,a.jsx)("div",{className:"space-y-1",children:(null==(t=d.permissions)?void 0:t.map(e=>(0,a.jsxs)("div",{className:"text-gray-700",children:["• ",A(e)]},e)))||(0,a.jsx)("div",{className:"text-gray-600 italic",children:"Chưa c\xf3 quyền n\xe0o"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-gray-900 mb-2",children:["Quyền mới (",g.length,"):"]}),(0,a.jsxs)("div",{className:"space-y-1",children:[g.map(e=>(0,a.jsxs)("div",{className:"text-blue-700",children:["• ",A(e)]},e)),0===g.length&&(0,a.jsx)("div",{className:"text-gray-600 italic",children:"Kh\xf4ng c\xf3 quyền n\xe0o"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:i,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,a.jsxs)("button",{onClick:j,disabled:y,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[y?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,a.jsx)(R.A,{size:16}),y?"Đang cập nhật...":"Cập nhật quyền"]})]})]})}):null}function q(){let{user:e}=(0,b.U)(),{isDepartmentManager:s}=(0,p.S)(),[t,r]=(0,n.useState)(null),[i,d]=(0,n.useState)([]),[o,x]=(0,n.useState)(!0),[y,f]=(0,n.useState)(!1),[N,w]=(0,n.useState)(!1),[_,C]=(0,n.useState)(!1),[S,R]=(0,n.useState)(null);(0,n.useEffect)(()=>{s&&(null==e?void 0:e.department)?(q(),P()):x(!1)},[s,null==e?void 0:e.department]);let q=async()=>{if(null==e?void 0:e.department)try{let s=localStorage.getItem("sessionToken")||"",t=await l.A.getDepartmentById(e.department,s);t.payload.success?r(t.payload.department):c.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban")}catch(e){console.error("Error fetching department:",e),c.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban")}},P=async()=>{if(!(null==e?void 0:e.department))return void x(!1);try{let s=localStorage.getItem("sessionToken")||"",t=await l.A.getDepartmentMembers(e.department,{page:1,perPage:50,search:""},s);t.payload.success?d(t.payload.members):c.oR.error("Kh\xf4ng thể tải danh s\xe1ch th\xe0nh vi\xean")}catch(e){console.error("Error fetching members:",e),c.oR.error("Lỗi khi tải danh s\xe1ch th\xe0nh vi\xean")}finally{x(!1)}};return s?o?(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):t?(0,a.jsxs)("div",{className:"space-y-6 text-gray-900",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Th\xf4ng tin ph\xf2ng ban"}),(0,a.jsx)(h.Ex,{variant:"success",children:"Quản l\xfd"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:t.name}),t.description&&(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:t.description})]}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{size:16,className:"text-gray-400"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[i.length," th\xe0nh vi\xean"]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch th\xe0nh vi\xean"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(j.A,{size:16}),"Th\xeam th\xe0nh vi\xean c\xf3 sẵn"]}),(0,a.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(u.A,{size:16}),"Tạo t\xe0i khoản mới"]})]})]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Th\xe0nh vi\xean"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vai tr\xf2"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quyền"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Thao t\xe1c"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>{var s;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.username}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)(h.eG,{role:e.departmentRole||"member"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[null==(s=e.permissions)?void 0:s.slice(0,3).map(e=>(0,a.jsx)(h.Ex,{variant:"secondary",size:"sm",children:A(e)},e)),e.permissions&&e.permissions.length>3&&(0,a.jsxs)(h.Ex,{variant:"secondary",size:"sm",children:["+",e.permissions.length-3]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,a.jsx)(v.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>{R(e),C(!0)},className:"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Chỉnh sửa quyền",children:(0,a.jsx)(g.A,{size:16})})]})})]},e._id)})})]})}),0===i.length&&(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Chưa c\xf3 th\xe0nh vi\xean n\xe0o"})})]}),(0,a.jsx)(k,{isOpen:y,onClose:()=>f(!1),departmentId:(null==e?void 0:e.department)||"",onMemberAdded:()=>{P(),f(!1)}}),(0,a.jsx)(T,{isOpen:N,onClose:()=>w(!1),departmentId:(null==e?void 0:e.department)||"",departmentPermissions:(null==t?void 0:t.defaultPermissions)||[],onUserCreated:()=>{P(),w(!1)}}),(0,a.jsx)(z,{isOpen:_,onClose:()=>{C(!1),R(null)},member:S,departmentId:(null==e?void 0:e.department)||"",departmentPermissions:(null==t?void 0:t.defaultPermissions)||[],onPermissionsUpdated:()=>{P(),C(!1),R(null)}})]}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy th\xf4ng tin ph\xf2ng ban"})}):null}var P=t(87708);function E(){return(0,a.jsx)(P.default,{requiredPermission:"admin",children:(0,a.jsx)("div",{className:"content",children:(0,a.jsx)(K,{})})})}function K(){let{isAdmin:e,isDepartmentManager:s}=(0,p.S)();return s?(0,a.jsx)(q,{}):e?(0,a.jsx)(y,{}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Bạn kh\xf4ng c\xf3 quyền truy cập trang n\xe0y"})})}},65871:(e,s,t)=>{Promise.resolve().then(t.bind(t,3892))}},e=>{e.O(0,[9268,3235,8543,6268,3190,2779,8441,5964,7358],()=>e(e.s=65871)),_N_E=e.O()}]);