(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3998],{3136:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(27937);let n={createDepartment:(e,t)=>a.Ay.post("/api/departments",e,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartments:(e,t)=>a.Ay.post("/api/departments/list",e,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartmentById:(e,t)=>a.Ay.get("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),updateDepartment:(e,t,r)=>a.Ay.put("/api/departments/".concat(e),t,{headers:{Authorization:"Bearer ".concat(r)}}),deleteDepartment:(e,t)=>a.Ay.delete("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),addMemberToDepartment:(e,t,r)=>a.Ay.post("/api/departments/".concat(e,"/members"),t,{headers:{Authorization:"Bearer ".concat(r)}}),getDepartmentMembers:(e,t,r)=>a.Ay.post("/api/departments/".concat(e,"/members/list"),t,{headers:{Authorization:"Bearer ".concat(r)}}),updateMemberPermissions:(e,t,r,n)=>a.Ay.put("/api/departments/".concat(e,"/members/").concat(t),r,{headers:{Authorization:"Bearer ".concat(n)}}),removeMemberFromDepartment:(e,t,r)=>a.Ay.delete("/api/departments/".concat(e,"/members/").concat(t),{headers:{Authorization:"Bearer ".concat(r)}}),getAvailablePermissions:e=>a.Ay.get("/api/departments/permissions",{headers:{Authorization:"Bearer ".concat(e)}})}},9424:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(12115),n=r(38637),s=r.n(n);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}var i=(0,a.forwardRef)(function(e,t){var r=e.color,n=e.size,s=void 0===n?24:n,i=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r,a,n={},s=Object.keys(e);for(a=0;a<s.length;a++)r=s[a],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,["color","size"]);return a.createElement("svg",o({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),a.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),a.createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="ArrowLeft";let l=i},23348:(e,t,r)=>{"use strict";r.d(t,{U:()=>o,default:()=>i});var a=r(95155),n=r(12115);let s=(0,n.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),o=()=>(0,n.useContext)(s),i=e=>{let{children:t}=e,[r,o]=(0,n.useState)(()=>null),[i,l]=(0,n.useState)(!0),c=(0,n.useCallback)(e=>{o(e),localStorage.setItem("user",JSON.stringify(e))},[o]);return(0,n.useEffect)(()=>{let e=localStorage.getItem("user");o(e?JSON.parse(e):null),l(!1)},[o]),(0,a.jsx)(s.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var a=r(84559),n=r(59434),s=r(35695);class o extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends o{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,p=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),m=await fetch(p,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),h=null,y=m.headers.get("content-type");if(y&&y.includes("application/json"))try{h=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await m.text();let f={status:m.status,payload:h};if(!m.ok)if(404===m.status||403===m.status)throw new i(f);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,s.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("https://toaantphcm.vn"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new o(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,n.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,n.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},38497:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});var a=r(23348);let n=()=>{let{user:e,isLoading:t}=(0,a.U)();return{hasPermission:r=>{var a;return!t&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},38637:(e,t,r)=>{e.exports=r(79399)()},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>o,cn:()=>s}),r(27937);var a=r(52596),n=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}r(58801);let o=e=>e.startsWith("/")?e.slice(1):e},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},79399:(e,t,r)=>{"use strict";var a=r(72948);function n(){}function s(){}s.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,s,o){if(o!==a){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:n};return r.PropTypes=r,r}},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(74556),n=r(49509);let s=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"https://toaantphcm.vn",NEXT_PUBLIC_URL:"https://toaantphcm.vn",CRYPTOJS_SECRECT:n.env.CRYPTOJS_SECRECT});if(!s.success)throw console.error("Invalid environment variables:",s.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let o=s.data},87708:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(95155),n=r(38497),s=r(35695),o=r(12115);function i(e){let{children:t,requiredPermission:r,requiredPermissions:i=[],requireAll:l=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:u,hasAnyPermission:d,isAdmin:p,isDepartmentManager:m,isLoading:h}=(0,n.S)(),y=(0,s.useRouter)();if((0,o.useEffect)(()=>{if(!h&&!p)(r?"admin"===r&&!!m||u(r):!(i.length>0)||(l?i.every(e=>u(e)):d(i)))||y.replace(c)},[u,d,p,m,h,r,i,l,c,y]),h)return(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(p)return(0,a.jsx)(a.Fragment,{children:t});return(r?"admin"===r&&!!m||u(r):!(i.length>0)||(l?i.every(e=>u(e)):d(i)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,a.jsx)("button",{onClick:()=>y.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}}}]);