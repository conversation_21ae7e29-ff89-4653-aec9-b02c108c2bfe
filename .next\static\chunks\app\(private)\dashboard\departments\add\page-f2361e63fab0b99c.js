(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4476],{11080:(e,r,a)=>{"use strict";a.d(r,{A:()=>l});var s=a(12115),t=a(38637),n=a.n(t);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(this,arguments)}var i=(0,s.forwardRef)(function(e,r){var a=e.color,t=e.size,n=void 0===t?24:t,i=function(e,r){if(null==e)return{};var a,s,t=function(e,r){if(null==e)return{};var a,s,t={},n=Object.keys(e);for(s=0;s<n.length;s++)a=n[s],r.indexOf(a)>=0||(t[a]=e[a]);return t}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)a=n[s],!(r.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(t[a]=e[a])}return t}(e,["color","size"]);return s.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:n,height:n,viewBox:"0 0 24 24",fill:"none",stroke:void 0===a?"currentColor":a,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),s.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),s.createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:n().string,size:n().oneOfType([n().string,n().number])},i.displayName="Save";let l=i},11725:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});var s=a(27937);let t={fetchUsers:(e,r)=>s.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),getAllUsers:(e,r)=>s.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),fetchLogs:(e,r)=>s.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),deleteUser:(e,r)=>s.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(r)}}),fetchUserById:(e,r,a)=>s.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(r)},signal:a}),CreateUser:(e,r)=>s.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(r)}}),updateUser:(e,r)=>s.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(r)}}),updatePassUser:(e,r)=>s.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(r)}})}},40506:(e,r,a)=>{Promise.resolve().then(a.bind(a,91902))},91902:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>g});var s=a(95155),t=a(12115),n=a(35695),o=a(38543),i=a(3136),l=a(11725),c=a(87708),d=a(9424),u=a(11080);function g(){let e=(0,n.useRouter)(),[r,a]=(0,t.useState)(!1),[g,h]=(0,t.useState)([]),[m,p]=(0,t.useState)([]),[x,b]=(0,t.useState)({name:"",description:"",defaultPermissions:[],managerId:""});(0,t.useEffect)(()=>{y(),f()},[]);let y=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await i.A.getAvailablePermissions(e);r.payload.success&&h(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},f=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await l.A.getAllUsers({page:1,perPage:100},e);if(r.payload.success){let e=r.payload.users.filter(e=>!e.department||"department_manager"!==e.rule);p(e)}}catch(e){console.error("Error fetching users:",e)}},v=async r=>{if(r.preventDefault(),!x.name.trim())return void o.oR.error("Vui l\xf2ng nhập t\xean ph\xf2ng ban");try{a(!0);let r=localStorage.getItem("sessionToken")||"",s=await i.A.createDepartment(x,r);s.payload.success?(o.oR.success("Tạo ph\xf2ng ban th\xe0nh c\xf4ng"),e.push("/dashboard/departments")):o.oR.error(s.payload.message||"Kh\xf4ng thể tạo ph\xf2ng ban")}catch(e){console.error("Error creating department:",e),o.oR.error("Lỗi khi tạo ph\xf2ng ban")}finally{a(!1)}},j=g.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return(0,s.jsx)(c.default,{requiredPermission:"admin",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(d.A,{size:20})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Th\xeam ph\xf2ng ban mới"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo ph\xf2ng ban mới với quyền mặc định"})]})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin cơ bản"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean ph\xf2ng ban ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:x.name,onChange:e=>b(r=>({...r,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean ph\xf2ng ban",required:!0})]})}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,s.jsx)("textarea",{value:x.description,onChange:e=>b(r=>({...r,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về ph\xf2ng ban"})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),(0,s.jsxs)("select",{value:x.managerId,onChange:e=>b(r=>({...r,managerId:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Chọn quản l\xfd ph\xf2ng ban"}),m.map(e=>(0,s.jsxs)("option",{value:e._id,children:[e.username," (",e.email,")"]},e._id))]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền mặc định cho người d\xf9ng"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c quyền n\xe0y sẽ được tự động g\xe1n cho tất cả người d\xf9ng mới của ph\xf2ng ban"}),(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(j).map(e=>{let[r,a]=e;return(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:r}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:a.map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:x.defaultPermissions.includes(e.key),onChange:r=>{var a,s;return a=e.key,s=r.target.checked,void b(e=>({...e,defaultPermissions:s?[...e.defaultPermissions,a]:e.defaultPermissions.filter(e=>e!==a)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},r)})})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e.back(),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsxs)("button",{type:"submit",disabled:r,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,s.jsx)(u.A,{size:16}),r?"Đang tạo...":"Tạo ph\xf2ng ban"]})]})]})]})})}}},e=>{e.O(0,[9268,3235,8543,3998,8441,5964,7358],()=>e(e.s=40506)),_N_E=e.O()}]);