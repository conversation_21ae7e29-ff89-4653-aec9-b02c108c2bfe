(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1454],{3136:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var a=t(27937);let s={createDepartment:(e,r)=>a.Ay.post("/api/departments",e,{headers:{Authorization:"Bearer ".concat(r)}}),getDepartments:(e,r)=>a.Ay.post("/api/departments/list",e,{headers:{Authorization:"Bearer ".concat(r)}}),getDepartmentById:(e,r)=>a.Ay.get("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),updateDepartment:(e,r,t)=>a.Ay.put("/api/departments/".concat(e),r,{headers:{Authorization:"Bearer ".concat(t)}}),deleteDepartment:(e,r)=>a.Ay.delete("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),addMemberToDepartment:(e,r,t)=>a.Ay.post("/api/departments/".concat(e,"/members"),r,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartmentMembers:(e,r,t)=>a.Ay.post("/api/departments/".concat(e,"/members/list"),r,{headers:{Authorization:"Bearer ".concat(t)}}),updateMemberPermissions:(e,r,t,s)=>a.Ay.put("/api/departments/".concat(e,"/members/").concat(r),t,{headers:{Authorization:"Bearer ".concat(s)}}),removeMemberFromDepartment:(e,r,t)=>a.Ay.delete("/api/departments/".concat(e,"/members/").concat(r),{headers:{Authorization:"Bearer ".concat(t)}}),getAvailablePermissions:e=>a.Ay.get("/api/departments/permissions",{headers:{Authorization:"Bearer ".concat(e)}})}},46396:(e,r,t)=>{Promise.resolve().then(t.bind(t,91314))},91314:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var a=t(95155),s=t(75937),n=t(62523),o=t(20174),l=t(62177),i=t(84649),c=t(63560),d=t(12115),m=t(35695),p=t(3136),u=t(23348);let h=e=>{let{onSubmit:r}=e,[t,h]=(0,d.useState)(!1);(0,m.useRouter)();let[x,g]=(0,d.useState)(null),[j,y]=(0,d.useState)([]),[b,f]=(0,d.useState)([]),[A,v]=(0,d.useState)(null),{user:k}=(0,u.U)(),B=(0,l.mN)({resolver:(0,c.u)(i.PD),defaultValues:{email:"",username:"",password:"",phonenumber:"",department:"",permissions:[]}});(0,d.useEffect)(()=>{z(),C()},[]);let z=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await p.A.getDepartments({page:1,perPage:100},e);if(r.payload.success){let e=r.payload.departments;(null==k?void 0:k.rule)==="department_manager"&&(null==k?void 0:k.department)&&(e=e.filter(e=>e._id===k.department._id)),y(e)}}catch(e){console.error("Error fetching departments:",e)}},C=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await p.A.getAvailablePermissions(e);r.payload.success&&f(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}};return(0,a.jsx)(s.lV,{...B,children:(0,a.jsxs)("form",{onSubmit:B.handleSubmit(r),className:"max-w-[600px] flex-shrink-0 w-full",noValidate:!0,children:[(0,a.jsx)(s.zB,{control:B.control,name:"username",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.MJ,{children:(0,a.jsx)(n.p,{placeholder:"T\xean người d\xf9ng",...r})}),(0,a.jsx)(s.C5,{})]})}}),(0,a.jsx)(s.zB,{control:B.control,name:"email",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.MJ,{children:(0,a.jsx)(n.p,{placeholder:"Địa chỉ email",type:"email",...r})}),(0,a.jsx)(s.C5,{})]})}}),(0,a.jsx)(s.zB,{control:B.control,name:"password",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.MJ,{children:(0,a.jsx)(n.p,{placeholder:"Mật khẩu",type:"password",...r})}),(0,a.jsx)(s.C5,{})]})}}),(0,a.jsx)(s.zB,{control:B.control,name:"phonenumber",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.MJ,{children:(0,a.jsx)(n.p,{placeholder:"Số điện thoại",type:"text",...r})}),(0,a.jsx)(s.C5,{})]})}}),(0,a.jsx)(s.zB,{control:B.control,name:"department",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.lR,{children:"Ph\xf2ng ban"}),(0,a.jsx)(s.MJ,{children:(0,a.jsxs)("select",{...r,onChange:e=>{r.onChange(e),(e=>{let r=j.find(r=>r._id===e);v(r||null),r?B.setValue("permissions",r.defaultPermissions):B.setValue("permissions",[])})(e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Chọn ph\xf2ng ban"}),j.map(e=>(0,a.jsx)("option",{value:e._id,children:e.name},e._id))]})}),(0,a.jsx)(s.C5,{})]})}}),A&&(0,a.jsx)(s.zB,{control:B.control,name:"permissions",render:e=>{let{field:r}=e;return(0,a.jsxs)(s.eI,{children:[(0,a.jsx)(s.lR,{children:"Quyền hạn"}),(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3",children:b.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==(t=r.value)?void 0:t.includes(e.key))||!1,onChange:t=>{let a=r.value||[];t.target.checked?r.onChange([...a,e.key]):r.onChange(a.filter(r=>r!==e.key))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key)})}),(0,a.jsx)(s.C5,{})]})}}),(0,a.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:x}),(0,a.jsxs)("button",{disabled:!!t,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:[t?(0,a.jsx)(o.A,{className:"animate-spin"}):"","Tạo t\xe0i khoản"]})]})})};var x=t(38543),g=t(11725),j=t(87708);function y(){let e=(0,m.useRouter)(),r=async r=>{try{let t=localStorage.getItem("sessionToken")||"",a=await g.A.CreateUser(r,t);a.payload.success?(x.oR.success("Th\xe0nh C\xf4ng"),e.push("/dashboard/user/".concat(a.payload.user._id))):(x.oR.error(a.payload.message),console.error("Error creating:",a.payload.message))}catch(e){console.error("Unexpected error:",e),x.oR.error(e.payload.message)}};return(0,a.jsxs)(j.default,{requiredPermission:"user_add",children:[(0,a.jsx)("h1",{className:"text-2xl mb-4",children:"Th\xeam Th\xe0nh Vi\xean"}),(0,a.jsx)(h,{onSubmit:r})]})}}},e=>{e.O(0,[9268,3235,8543,2182,9047,8441,5964,7358],()=>e(e.s=46396)),_N_E=e.O()}]);