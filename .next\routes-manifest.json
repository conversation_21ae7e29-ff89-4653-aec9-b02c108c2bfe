{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [{"page": "/api/uploads/media/[...path]", "regex": "^/api/uploads/media/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/uploads/media/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/api/uploads/single/[...path]", "regex": "^/api/uploads/single/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/uploads/single/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/dashboard/departments/[id]", "regex": "^/dashboard/departments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/departments/[id]/edit", "regex": "^/dashboard/departments/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/departments/[id]/members", "regex": "^/dashboard/departments/([^/]+?)/members(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)/members(?:/)?$"}, {"page": "/dashboard/departments/[id]/members/add", "regex": "^/dashboard/departments/([^/]+?)/members/add(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)/members/add(?:/)?$"}, {"page": "/dashboard/departments/[id]/members/[memberId]", "regex": "^/dashboard/departments/([^/]+?)/members/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPmemberId": "nxtPmemberId"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)/members/(?<nxtPmemberId>[^/]+?)(?:/)?$"}, {"page": "/dashboard/departments/[id]/members/[memberId]/edit", "regex": "^/dashboard/departments/([^/]+?)/members/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPmemberId": "nxtPmemberId"}, "namedRegex": "^/dashboard/departments/(?<nxtPid>[^/]+?)/members/(?<nxtPmemberId>[^/]+?)/edit(?:/)?$"}, {"page": "/dashboard/user/log/[id]", "regex": "^/dashboard/user/log/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/user/log/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/user/[id]", "regex": "^/dashboard/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/user/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/2fa", "regex": "^/2fa(?:/)?$", "routeKeys": {}, "namedRegex": "^/2fa(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/author", "regex": "^/author(?:/)?$", "routeKeys": {}, "namedRegex": "^/author(?:/)?$"}, {"page": "/change-password", "regex": "^/change\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/change\\-password(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/account", "regex": "^/dashboard/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/account(?:/)?$"}, {"page": "/dashboard/court-cases", "regex": "^/dashboard/court\\-cases(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/court\\-cases(?:/)?$"}, {"page": "/dashboard/court-cases/custom-fields", "regex": "^/dashboard/court\\-cases/custom\\-fields(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/court\\-cases/custom\\-fields(?:/)?$"}, {"page": "/dashboard/departments", "regex": "^/dashboard/departments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/departments(?:/)?$"}, {"page": "/dashboard/departments/add", "regex": "^/dashboard/departments/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/departments/add(?:/)?$"}, {"page": "/dashboard/files", "regex": "^/dashboard/files(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/files(?:/)?$"}, {"page": "/dashboard/setting", "regex": "^/dashboard/setting(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/setting(?:/)?$"}, {"page": "/dashboard/user", "regex": "^/dashboard/user(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/user(?:/)?$"}, {"page": "/dashboard/user/add", "regex": "^/dashboard/user/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/user/add(?:/)?$"}, {"page": "/dashboard/user/import", "regex": "^/dashboard/user/import(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/user/import(?:/)?$"}, {"page": "/forgot-pass", "regex": "^/forgot\\-pass(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-pass(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/logout", "regex": "^/logout(?:/)?$", "routeKeys": {}, "namedRegex": "^/logout(?:/)?$"}, {"page": "/logout-direct", "regex": "^/logout\\-direct(?:/)?$", "routeKeys": {}, "namedRegex": "^/logout\\-direct(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/verify", "regex": "^/verify(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}