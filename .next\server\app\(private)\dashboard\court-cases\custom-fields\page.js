(()=>{var a={};a.id=3948,a.ids=[3948],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11782:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\court-cases\\\\custom-fields\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx","default")},18883:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>N});var d=c(60687),e=c(43210),f=c(62688);let g=(0,f.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),h=(0,f.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var i=c(16189),j=c(93853);let k=(0,f.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),l=(0,f.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var m=c(13861);let n=(0,f.A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),o=(0,f.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),p=(0,f.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),q=(0,f.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var r=c(36101),s=c(29072),t=c(11860);let u=[{value:"text",label:"Văn bản",icon:"\uD83D\uDCDD"},{value:"number",label:"Số",icon:"\uD83D\uDD22"},{value:"date",label:"Ng\xe0y",icon:"\uD83D\uDCC5"},{value:"datetime",label:"Ng\xe0y giờ",icon:"\uD83D\uDD50"},{value:"boolean",label:"Đ\xfang/Sai",icon:"☑️"},{value:"select",label:"Lựa chọn đơn",icon:"\uD83D\uDCCB"},{value:"multiselect",label:"Lựa chọn nhiều",icon:"\uD83D\uDCCB"},{value:"currency",label:"Tiền tệ",icon:"\uD83D\uDCB0"},{value:"percentage",label:"Phần trăm",icon:"\uD83D\uDCCA"},{value:"email",label:"Email",icon:"\uD83D\uDCE7"},{value:"phone",label:"Số điện thoại",icon:"\uD83D\uDCDE"},{value:"url",label:"Đường dẫn",icon:"\uD83D\uDD17"},{value:"textarea",label:"Văn bản d\xe0i",icon:"\uD83D\uDCC4"}];function v({isOpen:a,onClose:b,onSubmit:c,targetModel:f}){let[g,h]=(0,e.useState)({name:"",label:"",description:"",dataType:"text",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[i,j]=(0,e.useState)({value:"",label:"",color:"#3B82F6"}),k="select"===g.dataType||"multiselect"===g.dataType;return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo trường t\xf9y chỉnh mới"}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(t.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:a=>(a.preventDefault(),g.name.trim()&&g.label.trim())?/^[a-zA-Z][a-zA-Z0-9_]*$/.test(g.name)?void c(g):void alert("T\xean trường chỉ được chứa chữ c\xe1i, số v\xe0 dấu gạch dưới, bắt đầu bằng chữ c\xe1i"):void alert("Vui l\xf2ng nhập t\xean v\xe0 nh\xe3n hiển thị"),className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean trường ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:g.name,onChange:a=>h(b=>({...b,name:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: priority_level",required:!0}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Chỉ chữ c\xe1i, số v\xe0 dấu gạch dưới. Bắt đầu bằng chữ c\xe1i."})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:g.label,onChange:a=>h(b=>({...b,label:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,d.jsx)("textarea",{value:g.description,onChange:a=>h(b=>({...b,description:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Kiểu dữ liệu ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:u.map(a=>(0,d.jsx)("button",{type:"button",onClick:()=>h(b=>({...b,dataType:a.value})),className:`p-3 border rounded-lg text-left transition-colors ${g.dataType===a.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-lg",children:a.icon}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label})]})},a.value))})]}),k&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xf9y chọn ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,d.jsx)("input",{type:"text",value:i.value,onChange:a=>j(b=>({...b,value:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,d.jsx)("input",{type:"text",value:i.label,onChange:a=>j(b=>({...b,label:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,d.jsx)("input",{type:"color",value:i.color,onChange:a=>j(b=>({...b,color:a.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,d.jsx)("button",{type:"button",onClick:()=>i.value.trim()&&i.label.trim()?g.config.options.some(a=>a.value===i.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(h(a=>({...a,config:{...a.config,options:[...a.config.options,{...i}]}})),j({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,d.jsx)(l,{size:16})})]}),(0,d.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:g.config.options.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:a.color}}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["(",a.value,")"]}),(0,d.jsx)("button",{type:"button",onClick:()=>{h(a=>({...a,config:{...a.config,options:a.config.options.filter((a,c)=>c!==b)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,d.jsx)(q,{size:14})})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:g.config.required,onChange:a=>h(b=>({...b,config:{...b.config,required:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:g.config.showInList,onChange:a=>h(b=>({...b,config:{...b.config,showInList:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:g.config.showInStats,onChange:a=>h(b=>({...b,config:{...b.config,showInStats:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo trường"})]})]})]})}):null}function w({isOpen:a,onClose:b,onSubmit:c,field:f}){var g;let[h,i]=(0,e.useState)({label:"",description:"",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[j,k]=(0,e.useState)({value:"",label:"",color:"#3B82F6"}),m=f?.dataType==="select"||f?.dataType==="multiselect";return a&&f?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa trường"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)("span",{className:"text-lg",children:{text:"\uD83D\uDCDD",number:"\uD83D\uDD22",date:"\uD83D\uDCC5",datetime:"\uD83D\uDD50",boolean:"☑️",select:"\uD83D\uDCCB",multiselect:"\uD83D\uDCCB",currency:"\uD83D\uDCB0",percentage:"\uD83D\uDCCA",email:"\uD83D\uDCE7",phone:"\uD83D\uDCDE",url:"\uD83D\uDD17",textarea:"\uD83D\uDCC4",file:"\uD83D\uDCCE",json:"\uD83D\uDD27"}[f.dataType]||"❓"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[f.name," (",{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[g=f.dataType]||g,")"]}),(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded ${f.isBuiltIn?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"}`,children:f.isBuiltIn?"Mặc định":"Đ\xe3 th\xeam"})]})]}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(t.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:a=>{if(a.preventDefault(),!h.label.trim())return void alert("Vui l\xf2ng nhập nh\xe3n hiển thị");c(h)},className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:h.label,onChange:a=>i(b=>({...b,label:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,d.jsx)("textarea",{value:h.description,onChange:a=>i(b=>({...b,description:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),m&&!f.isBuiltIn&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xf9y chọn"}),(0,d.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,d.jsx)("input",{type:"text",value:j.value,onChange:a=>k(b=>({...b,value:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,d.jsx)("input",{type:"text",value:j.label,onChange:a=>k(b=>({...b,label:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,d.jsx)("input",{type:"color",value:j.color,onChange:a=>k(b=>({...b,color:a.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,d.jsx)("button",{type:"button",onClick:()=>j.value.trim()&&j.label.trim()?h.config.options.some(a=>a.value===j.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(i(a=>({...a,config:{...a.config,options:[...a.config.options,{...j}]}})),k({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,d.jsx)(l,{size:16})})]}),(0,d.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:h.config.options.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:a.color}}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["(",a.value,")"]}),(0,d.jsx)("button",{type:"button",onClick:()=>{i(a=>({...a,config:{...a.config,options:a.config.options.filter((a,c)=>c!==b)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,d.jsx)(q,{size:14})})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.required,onChange:a=>i(b=>({...b,config:{...b.config,required:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInList,onChange:a=>i(b=>({...b,config:{...b.config,showInList:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInStats,onChange:a=>i(b=>({...b,config:{...b.config,showInStats:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Độ rộng cột (pixels)"}),(0,d.jsx)("input",{type:"number",value:h.config.columnWidth,onChange:a=>i(b=>({...b,config:{...b.config,columnWidth:parseInt(a.target.value)||150}})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"50",max:"500"})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})]})}):null}let x=[{name:"stt",label:"STT",dataType:"number",required:!0,canEdit:!0,canDelete:!0},{name:"soVanThu",label:"Số văn thư",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"loaiAn",label:"Loại \xe1n",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"soThuLy",label:"Số thụ l\xfd",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"tand",label:"TAND",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"soBanAn",label:"Số bản \xe1n",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"biCaoNguoiKhieuKien",label:"Bị c\xe1o/Người khiếu kiện",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"toiDanhNoiDung",label:"Tội danh/Nội dung",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"quanHePhatLuat",label:"Quan hệ ph\xe1p luật",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"hinhThucXuLy",label:"H\xecnh thức xử l\xfd",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"thuTucApDung",label:"Thủ tục \xe1p dụng",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"thamPhanPhuTrach",label:"Thẩm ph\xe1n phụ tr\xe1ch",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"truongPhoPhongKTNV",label:"Trưởng/Ph\xf3 ph\xf2ng KTNV",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ghiChu",label:"Ghi ch\xfa",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"ghiChuKetQua",label:"Ghi ch\xfa kết quả",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"trangThaiGiaiQuyet",label:"Trạng th\xe1i giải quyết",dataType:"select",required:!0,canEdit:!0,canDelete:!0,options:[{value:"Chưa giải quyết",label:"Chưa giải quyết",color:"#EF4444"},{value:"Đang giải quyết",label:"Đang giải quyết",color:"#F59E0B"},{value:"Đ\xe3 giải quyết",label:"Đ\xe3 giải quyết",color:"#10B981"}]}];function y({targetModel:a="CourtCase",onFieldsChange:b}){let[c,f]=(0,e.useState)([]),[g,h]=(0,e.useState)([]),[i,t]=(0,e.useState)(null),[u,y]=(0,e.useState)(!0),[z,A]=(0,e.useState)(!1),[B,C]=(0,e.useState)(null),[D,E]=(0,e.useState)(null),[F,G]=(0,e.useState)(""),[H,I]=(0,e.useState)("all"),J=async()=>{try{y(!0);let c=localStorage.getItem("sessionToken")||"",[d,e]=await Promise.all([r.A.getCustomFields(a,c),s.A.getFieldConfiguration(a,c)]);if(d.payload.success&&e.payload.success){f(d.payload.fields),t(e.payload.configuration);let a=e.payload.configuration.fieldConfigs,c=[];x.forEach(b=>{let d=a.find(a=>a.fieldName===b.name);c.push({...b,_id:`default_${b.name}`,isDefault:!0,config:{showInList:d?.showInList??!0,showInStats:d?.showInStats??"textarea"!==b.dataType,columnWidth:d?.columnWidth??150,sortOrder:d?.sortOrder??0,required:b.required,options:b.options||[]},createdBy:{username:"System",email:"system"}})}),d.payload.fields.forEach(b=>{let d=a.find(a=>a.fieldName===b.name);c.push({...b,isDefault:!1,canEdit:!0,canDelete:!0,config:{...b.config,showInList:d?.showInList??b.config.showInList,showInStats:d?.showInStats??b.config.showInStats,columnWidth:d?.columnWidth??b.config.columnWidth,sortOrder:d?.sortOrder??b.config.sortOrder}})}),c.sort((a,b)=>a.config.sortOrder-b.config.sortOrder),h(c),b?.(d.payload.fields)}}catch(a){console.error("Error fetching fields and configuration:",a),j.oR.error("Lỗi khi tải danh s\xe1ch trường t\xf9y chỉnh")}finally{y(!1)}},K=async b=>{try{let c=localStorage.getItem("sessionToken")||"",d=await r.A.createCustomField({...b,targetModel:a},c);d.payload.success?(j.oR.success("Tạo trường t\xf9y chỉnh th\xe0nh c\xf4ng"),A(!1),J()):j.oR.error(d.payload.message||"Kh\xf4ng thể tạo trường t\xf9y chỉnh")}catch(a){console.error("Error creating custom field:",a),j.oR.error("Lỗi khi tạo trường t\xf9y chỉnh")}},L=async(a,b)=>{try{let c=localStorage.getItem("sessionToken")||"",d=await r.A.updateCustomField(a,b,c);d.payload.success?(j.oR.success("Cập nhật trường t\xf9y chỉnh th\xe0nh c\xf4ng"),C(null),J()):j.oR.error(d.payload.message||"Kh\xf4ng thể cập nhật trường t\xf9y chỉnh")}catch(a){console.error("Error updating custom field:",a),j.oR.error("Lỗi khi cập nhật trường t\xf9y chỉnh")}},M=async a=>{let b=a.isDefault?"trường cơ bản":"trường t\xf9y chỉnh";if(confirm(`Bạn c\xf3 chắc chắn muốn x\xf3a ${b} "${a.label}"? Dữ liệu đ\xe3 nhập sẽ kh\xf4ng bị mất nhưng trường sẽ kh\xf4ng hiển thị nữa.`))try{if(a.isDefault){let c=g.filter(b=>b._id!==a._id);h(c),j.oR.success(`X\xf3a ${b} th\xe0nh c\xf4ng`)}else{let b=localStorage.getItem("sessionToken")||"",c=await r.A.deleteCustomField(a._id,b);c.payload.success?(j.oR.success("X\xf3a trường t\xf9y chỉnh th\xe0nh c\xf4ng"),J()):j.oR.error(c.payload.message||"Kh\xf4ng thể x\xf3a trường t\xf9y chỉnh")}}catch(a){console.error("Error deleting field:",a),j.oR.error(`Lỗi khi x\xf3a ${b}`)}},N=async b=>{try{let c=localStorage.getItem("sessionToken")||"";if(!b.isDefault)return void await L(b._id,{config:{...b.config,showInList:!b.config.showInList}});await s.A.updateFieldVisibility({targetModel:a,fieldName:b.name,showInList:!b.config.showInList},c),await J(),j.oR.success(`${b.config.showInList?"Ẩn":"Hiển thị"} cột "${b.label}" th\xe0nh c\xf4ng`)}catch(a){console.error("Error toggling field visibility:",a),j.oR.error("Lỗi khi cập nhật hiển thị trường")}},O=a=>{a.preventDefault(),a.dataTransfer.dropEffect="move"},P=async(b,c)=>{if(b.preventDefault(),!D||D===c)return void E(null);let d=g.findIndex(a=>a._id===D),e=g.findIndex(a=>a._id===c);if(-1===d||-1===e)return void E(null);let f=[...g],[h]=f.splice(d,1);f.splice(e,0,h);let i=f.map((a,b)=>({fieldName:a.name,sortOrder:b}));try{let b=localStorage.getItem("sessionToken")||"";await s.A.updateFieldOrder({targetModel:a,fieldOrders:i},b);let c=f.filter(a=>!a.isDefault).map((a,b)=>({id:a._id,sortOrder:b}));c.length>0&&await r.A.updateFieldsOrder(c,b),await J(),j.oR.success("Cập nhật thứ tự trường th\xe0nh c\xf4ng")}catch(a){console.error("Error updating field order:",a),j.oR.error("Lỗi khi cập nhật thứ tự trường")}E(null)},Q=g.filter(a=>{let b=""===F||a.name.toLowerCase().includes(F.toLowerCase())||a.label.toLowerCase().includes(F.toLowerCase()),c="all"===H||"builtin"===H&&a.isDefault||"custom"===H&&!a.isDefault||"visible"===H&&a.config.showInList||"hidden"===H&&!a.config.showInList;return b&&c});return u?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(k,{size:20}),"Quản l\xfd trường t\xf9y chỉnh"]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho ","CourtCase"===a?"vụ việc t\xf2a \xe1n":a]})]}),(0,d.jsxs)("button",{onClick:()=>A(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(l,{size:16}),"Th\xeam trường mới"]})]}),g.length>0&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(k,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:g.length}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"Tổng số trường"})]})]})}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(l,{size:20,className:"text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-900",children:g.filter(a=>!a.isDefault).length}),(0,d.jsx)("div",{className:"text-sm text-green-700",children:"Trường đ\xe3 th\xeam"})]})]})}),(0,d.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(m.A,{size:20,className:"text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:g.filter(a=>a.config.showInList).length}),(0,d.jsx)("div",{className:"text-sm text-purple-700",children:"Đang hiển thị"})]})]})}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDD12"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:g.filter(a=>a.isDefault).length}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"Trường cơ bản"})]})]})})]}),g.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,d.jsx)("button",{onClick:()=>{h(g.map(a=>({...a,config:{...a.config,showInList:!0}}))),j.oR.success("Hiển thị tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:"\uD83D\uDC41️ Hiển thị tất cả"}),(0,d.jsx)("button",{onClick:()=>{h(g.map(a=>({...a,config:{...a.config,showInList:!1}}))),j.oR.success("Ẩn tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"\uD83D\uDE48 Ẩn tất cả"}),(0,d.jsx)("button",{onClick:()=>{h([...g.filter(a=>a.isDefault).map(a=>({...a,config:{...a.config,showInList:!0}})),...g.filter(a=>!a.isDefault).map(a=>({...a,config:{...a.config,showInList:!1}}))]),j.oR.success("Chỉ hiển thị trường cơ bản")},className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:"\uD83D\uDCCB Chỉ cơ bản"}),(0,d.jsx)("button",{onClick:()=>{h([...g.filter(a=>a.isDefault).map(a=>({...a,config:{...a.config,showInList:!1}})),...g.filter(a=>!a.isDefault).map(a=>({...a,config:{...a.config,showInList:!0}}))]),j.oR.success("Chỉ hiển thị trường đ\xe3 th\xeam")},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:"➕ Chỉ đ\xe3 th\xeam"})]}),g.length>0&&(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)("input",{type:"text",placeholder:"T\xecm kiếm trường theo t\xean hoặc nh\xe3n...",value:F,onChange:a=>G(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,d.jsxs)("select",{value:H,onChange:a=>I(a.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"Tất cả trường"}),(0,d.jsx)("option",{value:"builtin",children:"Chỉ trường cơ bản"}),(0,d.jsx)("option",{value:"custom",children:"Chỉ trường đ\xe3 th\xeam"}),(0,d.jsx)("option",{value:"visible",children:"Chỉ trường hiển thị"}),(0,d.jsx)("option",{value:"hidden",children:"Chỉ trường ẩn"})]})]}),0===g.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(k,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Đang tải danh s\xe1ch trường..."})]}):0===Q.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(k,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Kh\xf4ng t\xecm thấy trường n\xe0o ph\xf9 hợp"}),(0,d.jsx)("button",{onClick:()=>{G(""),I("all")},className:"text-blue-600 hover:text-blue-700 font-medium",children:"X\xf3a bộ lọc"})]}):(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-2",children:[(0,d.jsx)("span",{className:"text-green-500 mt-0.5",children:"ℹ️"}),(0,d.jsxs)("div",{className:"text-sm text-green-700",children:[(0,d.jsx)("strong",{children:"Hướng dẫn:"})," Tất cả c\xe1c trường đều c\xf3 thể k\xe9o thả để sắp xếp thứ tự. Trường cơ bản (m\xe0u xanh dương) v\xe0 trường t\xf9y chỉnh (m\xe0u xanh l\xe1) đều c\xf3 thể di chuyển."]})]})}),Q.map(a=>{var b;return(0,d.jsxs)("div",{draggable:!0,onDragStart:b=>{E(a._id),b.dataTransfer.effectAllowed="move"},onDragOver:O,onDrop:b=>P(b,a._id),className:`flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-move ${D===a._id?"opacity-50":""} ${!a.config.showInList?"bg-gray-50 border-gray-200":"border-gray-300"} ${a.isDefault?"border-l-4 border-l-blue-500":"border-l-4 border-l-green-500"}`,children:[(0,d.jsx)(n,{size:16,className:"text-gray-400",title:"K\xe9o thả để sắp xếp thứ tự"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(a=>{switch(a){case"text":return"\uD83D\uDCDD";case"number":return"\uD83D\uDD22";case"date":return"\uD83D\uDCC5";case"datetime":return"\uD83D\uDD50";case"boolean":return"☑️";case"select":case"multiselect":return"\uD83D\uDCCB";case"currency":return"\uD83D\uDCB0";case"percentage":return"\uD83D\uDCCA";case"email":return"\uD83D\uDCE7";case"phone":return"\uD83D\uDCDE";case"url":return"\uD83D\uDD17";case"textarea":return"\uD83D\uDCC4";case"file":return"\uD83D\uDCCE";case"json":return"\uD83D\uDD27";default:return"❓"}})(a.dataType)}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.label}),(0,d.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[b=a.dataType]||b}),a.config.required&&(0,d.jsx)("span",{className:"text-xs bg-red-100 text-red-600 px-2 py-1 rounded",children:"Bắt buộc"}),(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded ${a.isDefault?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"}`,children:a.isDefault?"Cơ bản":"Đ\xe3 th\xeam"})]}),a.description&&(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>N(a),className:`p-2 rounded-lg transition-colors ${a.config.showInList?"text-green-600 hover:bg-green-50":"text-gray-400 hover:bg-gray-100"}`,title:a.config.showInList?"Ẩn khỏi danh s\xe1ch":"Hiển thị trong danh s\xe1ch",children:a.config.showInList?(0,d.jsx)(m.A,{size:16}):(0,d.jsx)(o,{size:16})}),(0,d.jsx)("button",{onClick:()=>C(a),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(p,{size:16})}),(0,d.jsx)("button",{onClick:()=>M(a),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(q,{size:16})})]})]},a._id)}),0===g.filter(a=>!a.isDefault).length&&(0,d.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 rounded-lg mt-4",children:[(0,d.jsx)("p",{className:"text-gray-500 mb-2",children:"Chưa c\xf3 trường n\xe0o được th\xeam"}),(0,d.jsx)("button",{onClick:()=>A(!0),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Tạo trường đầu ti\xean"})]})]}),(0,d.jsx)(v,{isOpen:z,onClose:()=>A(!1),onSubmit:K,targetModel:a}),B&&(0,d.jsx)(w,{isOpen:!0,onClose:()=>C(null),onSubmit:a=>{B.isDefault?(h(g.map(b=>b._id===B._id?{...b,label:a.label,description:a.description,config:{...b.config,...a.config}}:b)),C(null),j.oR.success("Cập nhật trường cơ bản th\xe0nh c\xf4ng")):L(B._id,a)},field:B})]})}let z=(0,f.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),A=(0,f.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),B=(0,f.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function C({targetModel:a="CourtCase",fields:b}){let[c,f]=(0,e.useState)({}),[g,i]=(0,e.useState)(!0),k=async()=>{try{i(!0);let b=localStorage.getItem("sessionToken")||"",c=await r.A.getCustomFieldStats(a,b);c.payload.success&&f(c.payload.stats)}catch(a){console.error("Error fetching custom field stats:",a),j.oR.error("Lỗi khi tải thống k\xea trường t\xf9y chỉnh")}finally{i(!1)}},l=a=>new Intl.NumberFormat("vi-VN").format(a),m=a=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a);if(g)return(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});let n=Object.entries(c);return 0===n.length?(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(h,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 thống k\xea cho trường t\xf9y chỉnh"}),(0,d.jsx)("p",{className:"text-sm text-gray-400",children:"Th\xeam dữ liệu v\xe0o c\xe1c trường t\xf9y chỉnh để xem thống k\xea"})]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,d.jsx)(h,{size:24,className:"text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Thống k\xea trường t\xf9y chỉnh"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Thống k\xea tự động được t\xednh to\xe1n dựa tr\xean dữ liệu trong c\xe1c trường t\xf9y chỉnh"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:n.map(([a,c])=>{let e=b.find(b=>b.name===a);return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,d.jsxs)("div",{className:"p-2 bg-blue-100 rounded-lg",children:["distribution"===c.type&&(0,d.jsx)(z,{size:20,className:"text-blue-600"}),"numeric"===c.type&&(0,d.jsx)(A,{size:20,className:"text-blue-600"}),"boolean"===c.type&&(0,d.jsx)(B,{size:20,className:"text-blue-600"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:c.label}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:e?.dataType==="currency"?"Tiền tệ":e?.dataType==="percentage"?"Phần trăm":e?.dataType==="select"?"Lựa chọn đơn":e?.dataType==="multiselect"?"Lựa chọn nhiều":e?.dataType==="boolean"?"Đ\xfang/Sai":e?.dataType==="number"?"Số":"Kh\xe1c"})]})]}),"distribution"===c.type&&Array.isArray(c.data)&&(0,d.jsx)("div",{className:"space-y-3",children:c.data.map((a,b)=>{let e=c.data.reduce((a,b)=>a+b.count,0),f=e>0?a.count/e*100:0;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a._id||"Kh\xf4ng x\xe1c định"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:l(a.count)}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",f.toFixed(1),"%)"]})]})]},b)})}),"numeric"===c.type&&c.data&&(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.total||0):l(c.data.total||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.avg||0):l(c.data.avg||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Trung b\xecnh"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.min||0):l(c.data.min||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Nhỏ nhất"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.max||0):l(c.data.max||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Lớn nhất"})]})]}),"boolean"===c.type&&Array.isArray(c.data)&&(0,d.jsx)("div",{className:"space-y-3",children:c.data.map((a,b)=>{let e=c.data.reduce((a,b)=>a+b.count,0),f=e>0?a.count/e*100:0;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:`w-3 h-3 rounded-full ${a._id?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a._id?"C\xf3":"Kh\xf4ng"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:l(a.count)}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",f.toFixed(1),"%)"]})]})]},b)})})]},a)})}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("button",{onClick:k,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"L\xe0m mới thống k\xea"})})]})}var D=c(48730);let E=(0,f.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var F=c(43649),G=c(44451);let H=(0,f.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);function I({isOpen:a,onClose:b,onSubmit:c,availableFields:f}){let[g,h]=(0,e.useState)(""),[i,j]=(0,e.useState)({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),k=a=>{if(a.preventDefault(),!g)return void alert("Vui l\xf2ng chọn trường ng\xe0y");if(i.warningDays&&i.dangerDays&&i.dangerDays>=i.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");let b=f.find(a=>a.name===g);b&&c(b.name,b.isDefault,i)};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(D.A,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo cấu h\xecnh đếm ngược"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Thiết lập cảnh b\xe1o hết hạn cho trường ng\xe0y (GMT+7)"}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded p-2 mt-2",children:(0,d.jsxs)("p",{className:"text-xs text-blue-700",children:["\uD83D\uDCA1 ",(0,d.jsx)("strong",{children:"Lưu \xfd:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, hệ thống sẽ cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn"]})})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,d.jsx)(t.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:k,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Chọn trường ng\xe0y ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("select",{value:g,onChange:a=>h(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,d.jsx)("option",{value:"",children:"-- Chọn trường ng\xe0y --"}),f.map(a=>(0,d.jsxs)("option",{value:a.name,children:[a.label," ",a.isDefault?"(Cơ bản)":"(T\xf9y chỉnh)"]},a.name))]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(F.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.warningDays||30,onChange:a=>j(b=>({...b,warningDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.dangerDays||7,onChange:a=>j(b=>({...b,dangerDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.enabled,onChange:a=>j(b=>({...b,enabled:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.showCountdownBadge,onChange:a=>j(b=>({...b,showCountdownBadge:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Hiển thị đếm ngược chi tiết (ng\xe0y, giờ, ph\xfat, gi\xe2y) theo GMT+7"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.showColorWarning,onChange:a=>j(b=>({...b,showColorWarning:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.hideExpired,onChange:a=>j(b=>({...b,hideExpired:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(H,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.emailNotification?.enabled,onChange:a=>j(b=>({...b,emailNotification:{...b.emailNotification,enabled:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),i.emailNotification?.enabled&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.emailNotification?.daysBefore||7,onChange:a=>j(b=>({...b,emailNotification:{...b.emailNotification,daysBefore:parseInt(a.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,d.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),i.showCountdownBadge&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,d.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:()=>{h(""),j({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),b()},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",form:"countdown-form",onClick:k,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo cấu h\xecnh"})]})]})}):null}function J({isOpen:a,onClose:b,onSubmit:c,countdown:f,getFieldLabel:g}){let[h,i]=(0,e.useState)({}),j=a=>{if(a.preventDefault(),f){if(h.warningDays&&h.dangerDays&&h.dangerDays>=h.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");c(f._id,h)}};return a&&f?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(D.A,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa cấu h\xecnh đếm ngược"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:g(f.fieldName)})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,d.jsx)(t.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:j,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(F.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.warningDays||30,onChange:a=>i(b=>({...b,warningDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.dangerDays||7,onChange:a=>i(b=>({...b,dangerDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.enabled,onChange:a=>i(b=>({...b,enabled:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.showCountdownBadge,onChange:a=>i(b=>({...b,showCountdownBadge:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:'Hiển thị badge "C\xf2n X ng\xe0y" trong danh s\xe1ch'})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.showColorWarning,onChange:a=>i(b=>({...b,showColorWarning:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.hideExpired,onChange:a=>i(b=>({...b,hideExpired:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(H,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.emailNotification?.enabled,onChange:a=>i(b=>({...b,emailNotification:{...b.emailNotification,enabled:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),h.emailNotification?.enabled&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.emailNotification?.daysBefore||7,onChange:a=>i(b=>({...b,emailNotification:{...b.emailNotification,daysBefore:parseInt(a.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,d.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),h.showCountdownBadge&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,d.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",onClick:j,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})}):null}var K=c(49269);let L=[{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư"},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd"},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh"}];function M({targetModel:a="CourtCase"}){let[b,c]=(0,e.useState)([]),[f,g]=(0,e.useState)([]),[h,i]=(0,e.useState)([]),[m,n]=(0,e.useState)(!0),[o,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(!1),v=async()=>{try{n(!0);let b=localStorage.getItem("sessionToken")||"",[d,e]=await Promise.all([G.A.getDateCountdowns(a,b),r.A.getCustomFields(a,b)]);d.payload.success&&c(d.payload.countdowns),e.payload.success&&g(e.payload.fields);let f=e.payload.success?e.payload.fields.filter(a=>"date"===a.dataType||"datetime"===a.dataType):[],h=[...L.map(a=>({...a,isDefault:!0})),...f.map(a=>({name:a.name,label:a.label,isDefault:!1}))];i(h)}catch(a){console.error("Error fetching data:",a),j.oR.error("Lỗi khi tải dữ liệu")}finally{n(!1)}},w=async(b,c,d)=>{try{let e=localStorage.getItem("sessionToken")||"",f=await G.A.upsertDateCountdown({targetModel:a,fieldName:b,isBuiltIn:c,countdownConfig:d},e);f.payload.success?(j.oR.success("Tạo cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),u(!1),v()):j.oR.error(f.payload.message||"Kh\xf4ng thể tạo cấu h\xecnh đếm ngược")}catch(a){console.error("Error creating countdown:",a),j.oR.error("Lỗi khi tạo cấu h\xecnh đếm ngược")}},x=async(c,d)=>{try{let e=localStorage.getItem("sessionToken")||"",f=b.find(a=>a._id===c);if(!f)return;let g=await G.A.upsertDateCountdown({targetModel:a,fieldName:f.fieldName,isBuiltIn:f.isBuiltIn,countdownConfig:d},e);g.payload.success?(j.oR.success("Cập nhật cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),s(null),v()):j.oR.error(g.payload.message||"Kh\xf4ng thể cập nhật cấu h\xecnh đếm ngược")}catch(a){console.error("Error updating countdown:",a),j.oR.error("Lỗi khi cập nhật cấu h\xecnh đếm ngược")}},y=async a=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a cấu h\xecnh đếm ngược n\xe0y?"))try{let b=localStorage.getItem("sessionToken")||"",c=await G.A.deleteDateCountdown(a,b);c.payload.success?(j.oR.success("X\xf3a cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),v()):j.oR.error(c.payload.message||"Kh\xf4ng thể x\xf3a cấu h\xecnh đếm ngược")}catch(a){console.error("Error deleting countdown:",a),j.oR.error("Lỗi khi x\xf3a cấu h\xecnh đếm ngược")}},z=a=>{let b=h.find(b=>b.name===a);return b?b.label:a},A=()=>{let a=b.map(a=>a.fieldName);return h.filter(b=>!a.includes(b.name))};return m?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(D.A,{size:20}),"Cấu h\xecnh đếm ngược ng\xe0y hết hạn"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Thiết lập cảnh b\xe1o v\xe0 đếm ngược cho c\xe1c trường ng\xe0y th\xe1ng (m\xfai giờ GMT+7)"}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:(0,d.jsxs)("div",{className:"flex items-start gap-2",children:[(0,d.jsx)("span",{className:"text-yellow-600 mt-0.5",children:"\uD83D\uDCA1"}),(0,d.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,d.jsx)("strong",{children:"C\xe1ch hoạt động:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn.",(0,d.jsx)("br",{}),(0,d.jsx)("strong",{children:"V\xed dụ:"})," Ng\xe0y bắt đầu 31/07/2025 + 90 ng\xe0y cảnh b\xe1o = Hết hạn 29/10/2025"]})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"\uD83C\uDF0F GMT+7 (Việt Nam)"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Đếm ngược theo thời gian thực với độ ch\xednh x\xe1c đến gi\xe2y"})]})]}),(0,d.jsxs)("button",{onClick:()=>u(!0),disabled:0===A().length,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,d.jsx)(l,{size:16}),"Th\xeam cấu h\xecnh"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(E,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:h.length}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"Trường ng\xe0y"})]})]})}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(D.A,{size:20,className:"text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-900",children:b.filter(a=>a.countdownConfig.enabled).length}),(0,d.jsx)("div",{className:"text-sm text-green-700",children:"Đang hoạt động"})]})]})}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,d.jsx)(F.A,{size:20,className:"text-yellow-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-900",children:b.filter(a=>a.countdownConfig.emailNotification.enabled).length}),(0,d.jsx)("div",{className:"text-sm text-yellow-700",children:"Email cảnh b\xe1o"})]})]})}),(0,d.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(k,{size:20,className:"text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:b.length}),(0,d.jsx)("div",{className:"text-sm text-purple-700",children:"Tổng cấu h\xecnh"})]})]})})]}),0===b.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(D.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 cấu h\xecnh đếm ngược n\xe0o"}),(0,d.jsx)("button",{onClick:()=>u(!0),disabled:0===A().length,className:"text-blue-600 hover:text-blue-700 font-medium disabled:text-gray-400",children:0===A().length?"Kh\xf4ng c\xf3 trường ng\xe0y n\xe0o để cấu h\xecnh":"Tạo cấu h\xecnh đầu ti\xean"})]}):(0,d.jsx)("div",{className:"space-y-4",children:b.map(a=>(0,d.jsx)("div",{className:`p-4 border rounded-lg ${a.countdownConfig.enabled?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:`p-2 rounded-lg ${a.countdownConfig.enabled?"bg-green-100":"bg-gray-100"}`,children:(0,d.jsx)(D.A,{size:16,className:a.countdownConfig.enabled?"text-green-600":"text-gray-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:z(a.fieldName)}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mt-1",children:[(0,d.jsxs)("span",{children:["Cảnh b\xe1o: ",a.countdownConfig.warningDays," ng\xe0y"]}),(0,d.jsxs)("span",{children:["Nguy hiểm: ",a.countdownConfig.dangerDays," ng\xe0y"]}),a.countdownConfig.emailNotification.enabled&&(0,d.jsx)("span",{className:"text-blue-600",children:"\uD83D\uDCE7 Email"})]}),a.countdownConfig.enabled&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500 mr-2",children:"Preview (đang đếm ngược):"}),(0,d.jsx)(K.A,{date:new Date(Date.now()+24*a.countdownConfig.dangerDays*36e5),warningDays:a.countdownConfig.warningDays,dangerDays:a.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0}),(0,d.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(Ng\xe0y bắt đầu + ",a.countdownConfig.warningDays," ng\xe0y = Ng\xe0y hết hạn)"]})]})]})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${a.countdownConfig.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:a.countdownConfig.enabled?"Hoạt động":"Tắt"}),(0,d.jsx)("button",{onClick:()=>s(a),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(p,{size:16})}),(0,d.jsx)("button",{onClick:()=>y(a._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(q,{size:16})})]})]})},a._id))}),(0,d.jsx)(I,{isOpen:t,onClose:()=>u(!1),onSubmit:w,availableFields:A()}),(0,d.jsx)(J,{isOpen:!!o,onClose:()=>s(null),onSubmit:x,countdown:o,getFieldLabel:z})]})}function N(){let a=(0,i.useRouter)(),[b,c]=(0,e.useState)("fields"),[f,j]=(0,e.useState)([]);return(0,d.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,d.jsx)("button",{onClick:()=>a.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(g,{size:20})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Quản l\xfd trường t\xf9y chỉnh - Vụ việc t\xf2a \xe1n"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho vụ việc t\xf2a \xe1n, giống như Excel"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,d.jsx)("button",{onClick:()=>c("fields"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"fields"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:"Quản l\xfd trường"}),(0,d.jsxs)("button",{onClick:()=>c("stats"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${"stats"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:[(0,d.jsx)(h,{size:16}),"Thống k\xea"]}),(0,d.jsx)("button",{onClick:()=>c("countdown"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${"countdown"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:"⏰ Đếm ngược"})]}),"fields"===b?(0,d.jsx)(y,{targetModel:"CourtCase",onFieldsChange:a=>{j(a)}}):"stats"===b?(0,d.jsx)(C,{targetModel:"CourtCase",fields:f}):(0,d.jsx)(M,{targetModel:"CourtCase"}),(0,d.jsxs)("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 T\xednh năng giống Excel"}),(0,d.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Tự động tạo cột:"})," Th\xeam trường mới sẽ tự động tạo cột trong bảng danh s\xe1ch"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Nhiều kiểu dữ liệu:"})," Văn bản, số, ng\xe0y th\xe1ng, lựa chọn, tiền tệ, v.v."]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Thống k\xea tự động:"})," Hệ thống tự động t\xednh to\xe1n thống k\xea cho c\xe1c trường mới"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"K\xe9o thả sắp xếp:"})," Thay đổi thứ tự hiển thị cột bằng c\xe1ch k\xe9o thả"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"T\xf9y chỉnh hiển thị:"})," Ẩn/hiện cột, điều chỉnh độ rộng"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Validation tự động:"})," Kiểm tra dữ liệu theo quy tắc đ\xe3 định"]})]})]})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69506:(a,b,c)=>{Promise.resolve().then(c.bind(c,11782))},69852:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["court-cases",{children:["custom-fields",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,11782)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/court-cases/custom-fields/page",pathname:"/dashboard/court-cases/custom-fields",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/court-cases/custom-fields/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87658:(a,b,c)=>{Promise.resolve().then(c.bind(c,18883))}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,9377,5600,6310],()=>b(b.s=69852));module.exports=c})();