(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1035],{17691:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),s=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,s=void 0===o?24:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="X";let l=i},20174:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),s=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,s=void 0===o?24:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),n.createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),n.createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),n.createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),n.createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),n.createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),n.createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),n.createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Loader";let l=i},23348:(e,t,r)=>{"use strict";r.d(t,{U:()=>a,default:()=>i});var n=r(95155),o=r(12115);let s=(0,o.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),a=()=>(0,o.useContext)(s),i=e=>{let{children:t}=e,[r,a]=(0,o.useState)(()=>null),[i,l]=(0,o.useState)(!0),c=(0,o.useCallback)(e=>{a(e),localStorage.setItem("user",JSON.stringify(e))},[a]);return(0,o.useEffect)(()=>{let e=localStorage.getItem("user");a(e?JSON.parse(e):null),l(!1)},[a]),(0,n.jsx)(s.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var n=r(84559),o=r(59434),s=r(35695);class a extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends a{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?n.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,p=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),m=await fetch(p,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),h=null,g=m.headers.get("content-type");if(g&&g.includes("application/json"))try{h=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await m.text();let f={status:m.status,payload:h};if(!m.ok)if(404===m.status||403===m.status)throw new i(f);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,s.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("https://toaantphcm.vn"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new a(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,o.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,o.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},34726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var n=r(95155),o=r(63560),s=r(62177),a=r(75937),i=r(62523),l=r(12115),c=r(20174),d=r(61612);d.Ay.object({message:d.Ay.string()}).strict();let u=d.Ay.object({title:d.Ay.string().min(1,"Title is required"),desc:d.Ay.string().optional(),address:d.Ay.string().optional(),email:d.Ay.string().optional(),hotline:d.Ay.string().optional(),contact:d.Ay.string().optional(),copyright:d.Ay.string().optional(),footerBLock1:d.Ay.string().optional(),footerBLock2:d.Ay.string().optional(),logo:d.Ay.object({_id:d.Ay.string(),path:d.Ay.string(),folder:d.Ay.string()}),ads1:d.Ay.string().optional(),openReg:d.Ay.boolean().optional()});d.Ay.object({_id:d.Ay.string().optional(),name:d.Ay.string().min(1,"Name is required"),slug:d.Ay.string().min(1,"Slug is required"),tasks:d.Ay.array(d.Ay.any()).optional(),id:d.Ay.number(),droppable:d.Ay.boolean(),parent:d.Ay.number(),text:d.Ay.string().min(1,"Name is required")}),d.Ay.object({_id:d.Ay.string().optional(),title:d.Ay.string(),position:d.Ay.number()});var p=r(83931),m=r(38543),h=r(74272),g=r(17691),f=r(84559);let y=e=>{let{serverImageUrl:t,onUploadFeatureImg:r,onDeleteFeatureImg:o}=e,[s,a]=(0,l.useState)(""),[i,c]=(0,l.useState)(""),[d,u]=(0,l.useState)(!1),[p,m]=(0,l.useState)(t||"");return(0,l.useEffect)(()=>{t&&m(function(e){if(!e)return"";let t=(e.startsWith("/")?e.slice(1):e).replace("server/uploads/","uploads/");return"".concat(f.A.NEXT_PUBLIC_API_ENDPOINT,"/").concat(t)}(t))},[t]),(0,n.jsxs)("div",{className:"w-full block mb-2",children:[(0,n.jsxs)("label",{className:"flex flex-col px-4 py-6 border-dashed text-center border border-gray-400 cursor-pointer",children:[(0,n.jsx)("input",{className:"hidden",type:"file",accept:"image/png, image/jpeg, image/bmp, image/gif",onChange:e=>{var t;let n=null==(t=e.target.files)?void 0:t[0];if(!n)return;let o=n.size/4024/4024;if(!n.type.match("image.*"))return void a("Please choose an image file");if(u(!0),a(""),o>1)return void a("Your file is too big! Please select an image under 1MB");let s=URL.createObjectURL(n);c(n.name),m(s),a(""),r(n)}}),(0,n.jsxs)("span",{className:"text-center block",children:[(0,n.jsx)("span",{className:"flex justify-center",children:(0,n.jsx)(h.A,{})}),(0,n.jsx)("span",{className:"file-label",children:"Tải ảnh l\xean..."})]}),i&&(0,n.jsxs)("span",{className:"mt-2 text-sm",children:["Đ\xe3 chọn: ",i]}),s&&(0,n.jsx)("span",{className:"text-red-500 mt-2",children:s})]}),p&&(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("img",{src:p,alt:"Preview",className:"max-w-full h-auto rounded"})}),(0,n.jsx)("button",{type:"button",onClick:()=>{c(""),m(""),a(""),o()},className:"mt-4 text-sm text-red-600 hover:underline absolute right-2 top-2",children:(0,n.jsx)(g.A,{})})]})]})};var x=r(27937);let v={postLogo:(e,t)=>x.Ay.post("api/media/single-noresize",e,{headers:{Authorization:"Bearer ".concat(t)}})};var j=r(99843);let b=()=>{var e;let[t,r]=(0,l.useState)(!1),[d,h]=(0,l.useState)({}),[g,f]=(0,l.useState)(!1),{refreshSettings:x}=(0,j.i)(),b=(0,s.mN)({resolver:(0,o.u)(u),defaultValues:{title:"",desc:"",address:"",email:"",hotline:"",contact:"",copyright:"",footerBLock1:"",footerBLock2:"",openReg:!0,ads1:"",logo:{path:"",folder:"",_id:""}}}),{reset:w}=b;(0,l.useEffect)(()=>{(async()=>{try{r(!0);let e=localStorage.getItem("sessionToken")||"",t=await p.A.fetchSetting(e);t.payload.success?(h(t.payload.setting),w(t.payload.setting)):console.error("Failed to fetch")}catch(e){console.error("Error fetching:",e)}finally{r(!1)}})()},[w]);let N=async e=>{try{let t=localStorage.getItem("sessionToken")||"",r=await p.A.CraeteSetting(e,t);r.payload.success?(m.oR.success("Th\xe0nh C\xf4ng"),h(r.payload.setting),x()):(m.oR.error("An error occurred during update. Please try again."),console.error("Error creating category:",r.payload.message))}catch(e){console.error("Unexpected error:",e),m.oR.error("An error occurred during update. Please try again.")}},O=async e=>{if(t)return"";r(!0);try{let t=new FormData;t.append("imageFile",e);let r=localStorage.getItem("sessionToken")||"",n=await v.postLogo(t,r);if(n)return m.oR.success("Đăng h\xecnh ảnh th\xe0nh c\xf4ng."),setTimeout(()=>{b.setValue("logo",n.payload.featureImg)},3e3),n.payload.featureImg.path||"";return""}catch(e){return m.oR.error("An error occurred during update your profile. Please try again."),""}finally{r(!1)}};return(0,n.jsx)("div",{children:(0,n.jsx)(a.lV,{...b,children:(0,n.jsxs)("form",{onSubmit:b.handleSubmit(N),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,n.jsx)(a.zB,{control:b.control,name:"title",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Ti\xeau đề trang web"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"Nhập ti\xeau đề",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"desc",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"M\xf4 tả"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"Nhập m\xf4 tả",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"address",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Địa chỉ"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"Nhập địa chỉ",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"email",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Email"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"Nhập email",type:"email",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"hotline",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Hotline"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"Nhập hotline",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"copyright",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Copyright"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)(i.p,{placeholder:"copyright",...t})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"contact",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Li\xean hệ (HTML)"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)("textarea",{...t,placeholder:"Th\xf4ng tin li\xean hệ ",className:"border p-2 rounded w-full h-32"})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"footerBLock1",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Footer Block 1 (HTML)"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)("textarea",{...t,placeholder:"Nội dung Block 1",className:"border p-2 rounded w-full h-32"})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"footerBLock2",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Footer Block 2 (HTML)"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)("textarea",{...t,placeholder:"Nội dung Block 2",className:"border p-2 rounded w-full h-32"})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"ads1",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:"Khung quảng c\xe1o 1"}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)("textarea",{...t,placeholder:"M\xe3 Quảng c\xe1o",className:"border p-2 rounded w-full h-32"})}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsx)(a.zB,{control:b.control,name:"openReg",render:e=>{let{field:t}=e;return(0,n.jsxs)(a.eI,{children:[(0,n.jsx)(a.lR,{children:(0,n.jsx)("span",{className:"block mb-4",children:"Cho ph\xe9p th\xe0nh vi\xean đăng k\xfd"})}),(0,n.jsx)(a.MJ,{children:(0,n.jsx)("input",{type:"checkbox",checked:t.value,onChange:t.onChange,className:"w-5 h-5 border-gray-300 rounded mr-4"})}),(0,n.jsx)(a.lR,{className:"cursor-pointer",children:"Mở đăng k\xfd"}),(0,n.jsx)(a.C5,{})]})}}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"block mb-2",children:"Logo web"}),(0,n.jsx)(y,{serverImageUrl:null==d||null==(e=d.logo)?void 0:e.path,onUploadFeatureImg:O,onDeleteFeatureImg:()=>{b.setValue("logo",{path:"",folder:"",_id:""})}})]})]}),(0,n.jsx)("button",{disabled:t,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:t?(0,n.jsx)(c.A,{className:"animate-spin"}):"Submit"})]})})})};var w=r(87708),N=r(38497);function O(){let{hasPermission:e}=(0,N.S)(),t=e("system_settings_edit");return(0,n.jsxs)(w.default,{requiredPermissions:["system_settings_view","system_settings_edit"],requireAll:!1,children:[(0,n.jsx)("h1",{className:"text-2xl mb-4",children:"C\xe0i đặt"}),!t&&(0,n.jsxs)("div",{className:"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4",children:[(0,n.jsx)("strong",{children:"Lưu \xfd:"}),' Bạn chỉ c\xf3 quyền xem c\xe0i đặt. Để chỉnh sửa, cần li\xean hệ quản trị vi\xean cấp quyền "Chỉnh sửa c\xe0i đặt hệ thống".']}),t?(0,n.jsx)(b,{}):(0,n.jsx)("div",{className:"opacity-60 pointer-events-none",children:(0,n.jsx)(b,{})})]})}},38497:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var n=r(23348);let o=()=>{let{user:e,isLoading:t}=(0,n.U)();return{hasPermission:r=>{var n;return!t&&!!e&&("admin"===e.rule||(null==(n=e.permissions)?void 0:n.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},38637:(e,t,r)=>{e.exports=r(79399)()},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>a,cn:()=>s}),r(27937);var n=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}r(58801);let a=e=>e.startsWith("/")?e.slice(1):e},59698:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),s=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,s=void 0===o?24:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),n.createElement("circle",{cx:"12",cy:"12",r:"3"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Eye";let l=i},61612:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c});var n={};r.r(n),r.d(n,{BRAND:()=>i.qt,DIRTY:()=>s.jm,EMPTY_PATH:()=>s.I3,INVALID:()=>s.uY,NEVER:()=>i.tm,OK:()=>s.OK,ParseStatus:()=>s.MY,Schema:()=>i.Sj,ZodAny:()=>i.Ml,ZodArray:()=>i.n,ZodBigInt:()=>i.Lr,ZodBoolean:()=>i.WF,ZodBranded:()=>i.eN,ZodCatch:()=>i.hw,ZodDate:()=>i.aP,ZodDefault:()=>i.Xi,ZodDiscriminatedUnion:()=>i.jv,ZodEffects:()=>i.k1,ZodEnum:()=>i.Vb,ZodError:()=>l.G,ZodFirstPartyTypeKind:()=>i.kY,ZodFunction:()=>i.CZ,ZodIntersection:()=>i.Jv,ZodIssueCode:()=>l.eq,ZodLazy:()=>i.Ih,ZodLiteral:()=>i.DN,ZodMap:()=>i.Ut,ZodNaN:()=>i.Tq,ZodNativeEnum:()=>i.WM,ZodNever:()=>i.iS,ZodNull:()=>i.PQ,ZodNullable:()=>i.l1,ZodNumber:()=>i.rS,ZodObject:()=>i.bv,ZodOptional:()=>i.Ii,ZodParsedType:()=>a.Zp,ZodPipeline:()=>i._c,ZodPromise:()=>i.$i,ZodReadonly:()=>i.EV,ZodRecord:()=>i.b8,ZodSchema:()=>i.lK,ZodSet:()=>i.Kz,ZodString:()=>i.ND,ZodSymbol:()=>i.K5,ZodTransformer:()=>i.BG,ZodTuple:()=>i.y0,ZodType:()=>i.aR,ZodUndefined:()=>i._Z,ZodUnion:()=>i.fZ,ZodUnknown:()=>i._,ZodVoid:()=>i.a0,addIssueToContext:()=>s.zn,any:()=>i.bz,array:()=>i.YO,bigint:()=>i.o,boolean:()=>i.zM,coerce:()=>i.au,custom:()=>i.Ie,date:()=>i.p6,datetimeRegex:()=>i.fm,defaultErrorMap:()=>o.su,discriminatedUnion:()=>i.gM,effect:()=>i.QZ,enum:()=>i.k5,function:()=>i.fH,getErrorMap:()=>o.$W,getParsedType:()=>a.CR,instanceof:()=>i.Nl,intersection:()=>i.E$,isAborted:()=>s.G4,isAsync:()=>s.xP,isDirty:()=>s.DM,isValid:()=>s.fn,late:()=>i.fn,lazy:()=>i.RZ,literal:()=>i.eu,makeIssue:()=>s.y7,map:()=>i.Tj,nan:()=>i.oi,nativeEnum:()=>i.fc,never:()=>i.Zm,null:()=>i.ch,nullable:()=>i.me,number:()=>i.ai,object:()=>i.Ik,objectUtil:()=>a.o6,oboolean:()=>i.yN,onumber:()=>i.p7,optional:()=>i.lq,ostring:()=>i.Di,pipeline:()=>i.Tk,preprocess:()=>i.vk,promise:()=>i.iv,quotelessJson:()=>l.WI,record:()=>i.g1,set:()=>i.hZ,setErrorMap:()=>o.pJ,strictObject:()=>i.re,string:()=>i.Yj,symbol:()=>i.HR,transformer:()=>i.Gu,tuple:()=>i.PV,undefined:()=>i.Vx,union:()=>i.KC,unknown:()=>i.L5,util:()=>a.ZS,void:()=>i.rI});var o=r(85722),s=r(43454),a=r(16227),i=r(74556),l=r(4028);let c=n},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var n=r(95155),o=r(59434),s=r(99310),a=r(59698),i=r(12115);let l=i.forwardRef((e,t)=>{let{className:r,type:l,...c}=e,[d,u]=(0,i.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"relative w-full",children:[(0,n.jsx)("input",{type:"password"===l&&d?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,o.cn)("input input-bordered w-full rounded-md",r),ref:t,...c}),"password"===l&&(d?(0,n.jsx)(s.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}):(0,n.jsx)(a.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}))]})})});l.displayName="Input"},65512:(e,t,r)=>{Promise.resolve().then(r.bind(r,34726))},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},74272:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),s=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,s=void 0===o?24:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),n.createElement("polyline",{points:"17 8 12 3 7 8"}),n.createElement("line",{x1:"12",y1:"3",x2:"12",y2:"15"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Upload";let l=i},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>u,MJ:()=>x,zB:()=>m,eI:()=>f,lR:()=>y,C5:()=>v});var n=r(95155),o=r(12115),s=r(54624),a=r(62177),i=r(59434),l=r(87073);let c=(0,r(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(l.b,{ref:t,className:(0,i.cn)(c(),r),...o})});d.displayName=l.b.displayName;let u=a.Op,p=o.createContext({}),m=e=>{let{...t}=e;return(0,n.jsx)(p.Provider,{value:{name:t.name},children:(0,n.jsx)(a.xI,{...t})})},h=()=>{let e=o.useContext(p),t=o.useContext(g),{getFieldState:r,formState:n}=(0,a.xW)(),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},g=o.createContext({}),f=o.forwardRef((e,t)=>{let{className:r,...s}=e,a=o.useId();return(0,n.jsx)(g.Provider,{value:{id:a},children:(0,n.jsx)("div",{ref:t,className:(0,i.cn)("mb-4",r),...s})})});f.displayName="FormItem";let y=o.forwardRef((e,t)=>{let{className:r,...o}=e,{error:s,formItemId:a}=h();return(0,n.jsx)(d,{ref:t,className:(0,i.cn)(s&&"text-destructive",r),htmlFor:a,...o})});y.displayName="FormLabel";let x=o.forwardRef((e,t)=>{let{...r}=e,{error:o,formItemId:a,formDescriptionId:i,formMessageId:l}=h();return(0,n.jsx)(s.DX,{ref:t,id:a,"aria-describedby":o?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!o,...r})});x.displayName="FormControl",o.forwardRef((e,t)=>{let{className:r,...o}=e,{formDescriptionId:s}=h();return(0,n.jsx)("p",{ref:t,id:s,className:(0,i.cn)("text-[0.8rem] text-muted-foreground",r),...o})}).displayName="FormDescription";let v=o.forwardRef((e,t)=>{let{className:r,children:o,...s}=e,{error:a,formMessageId:l}=h(),c=a?String(null==a?void 0:a.message):o;return c?(0,n.jsx)("p",{ref:t,id:l,className:(0,i.cn)("text-[0.8rem] font-medium text-red-600",r),...s,children:c}):null});v.displayName="FormMessage"},79399:(e,t,r)=>{"use strict";var n=r(72948);function o(){}function s(){}s.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,s,a){if(a!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:o};return r.PropTypes=r,r}},83931:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(27937);let o={fetchSetting:e=>n.Ay.get("api/setting/admin",{headers:{Authorization:"Bearer ".concat(e)}}),commonFetchSetting:()=>n.Ay.get("api/setting/"),CraeteSetting:(e,t)=>n.Ay.put("api/setting/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditorSetting:(e,t)=>n.Ay.put("api/setting/editor",e,{headers:{Authorization:"Bearer ".concat(t)}}),CraeteMenu:(e,t)=>n.Ay.post("api/menu/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditMenu:(e,t)=>n.Ay.put("api/menu/edit",e,{headers:{Authorization:"Bearer ".concat(t)}}),GetMenu:e=>n.Ay.get("api/menu/".concat(e)),fetchMenus:(e,t)=>n.Ay.post("api/menu/get-all",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteMenu:(e,t)=>n.Ay.delete("api/menu/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}})}},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(74556),o=r(49509);let s=n.Ik({NEXT_PUBLIC_API_ENDPOINT:n.Yj().url(),NEXT_PUBLIC_URL:n.Yj().url(),CRYPTOJS_SECRECT:n.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"https://toaantphcm.vn",NEXT_PUBLIC_URL:"https://toaantphcm.vn",CRYPTOJS_SECRECT:o.env.CRYPTOJS_SECRECT});if(!s.success)throw console.error("Invalid environment variables:",s.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let a=s.data},87073:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(12115);r(47650);var o=r(54624),s=r(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o?r:t,{...a,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),i=n.forwardRef((e,t)=>(0,s.jsx)(a.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},87708:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(95155),o=r(38497),s=r(35695),a=r(12115);function i(e){let{children:t,requiredPermission:r,requiredPermissions:i=[],requireAll:l=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:p,isDepartmentManager:m,isLoading:h}=(0,o.S)(),g=(0,s.useRouter)();if((0,a.useEffect)(()=>{if(!h&&!p)(r?"admin"===r&&!!m||d(r):!(i.length>0)||(l?i.every(e=>d(e)):u(i)))||g.replace(c)},[d,u,p,m,h,r,i,l,c,g]),h)return(0,n.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(p)return(0,n.jsx)(n.Fragment,{children:t});return(r?"admin"===r&&!!m||d(r):!(i.length>0)||(l?i.every(e=>d(e)):u(i)))?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,n.jsx)("button",{onClick:()=>g.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},99310:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),s=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,s=void 0===o?24:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),n.createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="EyeOff";let l=i},99843:(e,t,r)=>{"use strict";r.d(t,{SettingProvider:()=>u,i:()=>p});var n=r(95155),o=r(12115),s=r(83931),a=r(35695);let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{let r=localStorage.getItem("".concat(e,"_timestamp"));if(!r)return!0;let n=parseInt(r);return Date.now()-n>60*t*1e3}catch(e){return console.error("❌ Error checking cache staleness:",e),!0}},l=(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t)),localStorage.setItem("".concat(e,"_timestamp"),Date.now().toString())}catch(e){console.error("❌ Error setting cache with timestamp:",e)}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{if(i(e,t))return null;let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return console.error("❌ Error getting fresh cache:",e),null}},d=(0,o.createContext)(void 0),u=e=>{let{children:t}=e,[r,i]=(0,o.useState)(null),[u,p]=(0,o.useState)(null),[m,h]=(0,o.useState)(!0),g=(0,a.usePathname)(),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(h(!0),!e){let e=c("siteSetting",5),t=c("siteMenus",5);if(e&&t){i(e),p(t),h(!1);return}}let t=await s.A.commonFetchSetting();t.payload.success?(i(t.payload.setting),p(t.payload.menus),l("siteSetting",t.payload.setting),l("siteMenus",t.payload.menus)):console.error("Failed to fetch settings")}catch(e){console.error("Error fetching settings:",e)}finally{h(!1)}};return(0,o.useEffect)(()=>{if("/"===g||g.startsWith("/dashboard"))f();else{let e=c("siteSetting",30),t=c("siteMenus",30);e&&t?(i(e),p(t),h(!1)):f()}},[g]),(0,n.jsx)(d.Provider,{value:{setting:r,loading:m,menus:u,refreshSettings:()=>{console.log("\uD83D\uDD04 Force refreshing settings...");try{localStorage.removeItem("siteSetting"),localStorage.removeItem("siteMenus"),console.log("✅ Setting cache cleared")}catch(e){console.error("❌ Error clearing setting cache:",e)}f(!0)}},children:t})},p=()=>{let e=(0,o.useContext)(d);if(!e)throw Error("useSetting must be used within a SettingProvider");return e}}},e=>{e.O(0,[9268,3235,8543,2182,8441,5964,7358],()=>e(e.s=65512)),_N_E=e.O()}]);