(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2828],{17703:(e,t,a)=>{"use strict";a.d(t,{CN:()=>o,Lz:()=>c,Wu:()=>i,ZB:()=>n,Zp:()=>r,aR:()=>l});var s=a(95155);a(12115);let r=e=>{let{children:t,className:a="",padding:r="md",shadow:l="sm",hover:n=!1,onClick:i}=e;return(0,s.jsx)("div",{className:"\n        bg-white rounded-xl border border-gray-100\n        ".concat({none:"",sm:"p-4",md:"p-6",lg:"p-8"}[r],"\n        ").concat({none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[l],"\n        ").concat(n?"hover:shadow-lg transition-shadow duration-200":"","\n        ").concat(a,"\n      "),onClick:i,children:t})},l=e=>{let{children:t,className:a=""}=e;return(0,s.jsx)("div",{className:"border-b border-gray-100 pb-4 mb-6 ".concat(a),children:t})},n=e=>{let{children:t,className:a="",size:r="md"}=e;return(0,s.jsx)("h2",{className:"font-bold text-gray-900 ".concat({sm:"text-lg",md:"text-xl",lg:"text-2xl"}[r]," ").concat(a),children:t})},i=e=>{let{children:t,className:a=""}=e;return(0,s.jsx)("div",{className:a,children:t})},o=e=>{let{title:t,value:a,icon:l,trend:n,color:i="blue",onClick:o,clickable:c=!1}=e,d=(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:t}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof a?a.toLocaleString():a}),n&&(0,s.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat(n.isPositive?"text-green-600":"text-red-600"),children:[(0,s.jsx)("span",{className:"mr-1",children:n.isPositive?"↗":"↘"}),n.value]})]}),l&&(0,s.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[i]),children:(0,s.jsx)("div",{className:"text-white",children:l})})]});return c&&o?(0,s.jsx)(r,{hover:!0,className:"relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ".concat(c?"hover:shadow-lg":""),onClick:o,children:d}):(0,s.jsx)(r,{hover:!0,className:"relative overflow-hidden",children:d})},c=e=>{let{title:t,description:a,icon:l,onClick:n,href:i,color:o="blue"}=e,c=e=>{let{children:t}=e;return(0,s.jsx)(r,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:t})},d=(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[l&&(0,s.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[o]," flex-shrink-0"),children:(0,s.jsx)("div",{className:"text-white",children:l})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:t}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:a})]})]});return i?(0,s.jsx)("a",{href:i,children:(0,s.jsx)(c,{children:d})}):(0,s.jsx)("div",{onClick:n,children:(0,s.jsx)(c,{children:d})})}},19828:(e,t,a)=>{"use strict";a.d(t,{Yq:()=>r,ZV:()=>l,z3:()=>s});let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(t<0?0:t))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][a]},r=e=>{if(!e)return"";let t=new Date(e);return isNaN(t.getTime())?"":t.toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit"})},l=e=>e.toLocaleString()},23348:(e,t,a)=>{"use strict";a.d(t,{U:()=>n,default:()=>i});var s=a(95155),r=a(12115);let l=(0,r.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),n=()=>(0,r.useContext)(l),i=e=>{let{children:t}=e,[a,n]=(0,r.useState)(()=>null),[i,o]=(0,r.useState)(!0),c=(0,r.useCallback)(e=>{n(e),localStorage.setItem("user",JSON.stringify(e))},[n]);return(0,r.useEffect)(()=>{let e=localStorage.getItem("user");n(e?JSON.parse(e):null),o(!1)},[n]),(0,s.jsx)(l.Provider,{value:{user:a,setUser:c,isAuthenticated:!!a,isLoading:i},children:t})}},38497:(e,t,a)=>{"use strict";a.d(t,{S:()=>r});var s=a(23348);let r=()=>{let{user:e,isLoading:t}=(0,s.U)();return{hasPermission:a=>{var s;return!t&&!!e&&("admin"===e.rule||(null==(s=e.permissions)?void 0:s.includes(a))||!1)},hasAnyPermission:a=>!t&&!!e&&("admin"===e.rule||a.some(t=>{var a;return null==(a=e.permissions)?void 0:a.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},51852:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var s=a(95155),r=a(12115),l=a(38543),n=a(27937);let i={getCourtCases:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;null!=s&&""!==s&&t.append(a,s.toString())});let a=t.toString();return n.Ay.get(a?"/api/court-cases?".concat(a):"/api/court-cases")},createCourtCase:e=>n.Ay.post("/api/court-cases",e),updateCourtCase:(e,t)=>n.Ay.put("/api/court-cases/".concat(e),t),deleteCourtCase:e=>n.Ay.delete("/api/court-cases/".concat(e)),getCourtCaseStats:()=>n.Ay.get("/api/court-cases/stats"),bulkDeleteCourtCases:e=>n.Ay.post("/api/court-cases/bulk-delete",{ids:e}),downloadTemplate:async()=>{let e={};{let t=localStorage.getItem("sessionToken");t&&(e.Authorization="Bearer ".concat(t))}let t=await fetch("/api/court-cases/template",{method:"GET",headers:e});if(!t.ok)throw Error("Failed to download template");let a=await t.blob(),s=window.URL.createObjectURL(a),r=document.createElement("a");return r.href=s,r.download="mau-import-vu-viec-toa-an.xlsx",document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s),{success:!0}},exportCourtCases:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;null!=s&&""!==s&&t.append(a,s.toString())});let a=t.toString(),s={};{let e=localStorage.getItem("sessionToken");e&&(s.Authorization="Bearer ".concat(e))}let r=await fetch(a?"/api/court-cases/export?".concat(a):"/api/court-cases/export",{method:"GET",headers:s});if(!r.ok)throw Error("Failed to export data");return r.arrayBuffer()},previewImport:e=>{let t=new FormData;return t.append("file",e),n.Ay.post("/api/court-cases/preview-import",t)},importCourtCases:e=>{let t=new FormData;return t.append("file",e),n.Ay.post("/api/court-cases/import",t)},getDetailedStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;null!=s&&""!==s&&t.append(a,s.toString())});let a=t.toString();return n.Ay.get(a?"/api/court-cases/detailed-stats?".concat(a):"/api/court-cases/detailed-stats")}};var o=a(19828),c=a(38497),d=a(9347),u=a(5116),h=a(92493),m=a(18594);let x=[{name:"stt",label:"STT",dataType:"number",width:"w-16",showInList:!0},{name:"soVanThu",label:"SỐ VĂN THƯ",dataType:"text",width:"w-32",showInList:!0},{name:"ngayNhanVanThu",label:"NG\xc0Y NHẬN VĂN THƯ",dataType:"date",width:"w-36",showInList:!0},{name:"loaiAn",label:"LOẠI \xc1N",dataType:"text",width:"w-28",showInList:!0},{name:"soThuLy",label:"SỐ THỤ L\xdd",dataType:"text",width:"w-32",showInList:!0},{name:"ngayThuLy",label:"NG\xc0Y THỤ L\xdd",dataType:"date",width:"w-32",showInList:!0},{name:"tand",label:"TAND",dataType:"text",width:"w-32",showInList:!0},{name:"soBanAn",label:"SỐ BẢN \xc1N",dataType:"text",width:"w-32",showInList:!0},{name:"ngayBanHanh",label:"NG\xc0Y BAN H\xc0NH",dataType:"date",width:"w-36",showInList:!0},{name:"biCaoNguoiKhieuKien",label:"BỊ C\xc1O/NĐ/NKK",dataType:"text",width:"w-44",showInList:!0},{name:"toiDanhNoiDung",label:"TỘI DANH/BĐ/NBK",dataType:"textarea",width:"w-44",showInList:!0},{name:"quanHePhatLuat",label:"TỘI DANH/QHPL",dataType:"text",width:"w-44",showInList:!0},{name:"hinhThucXuLy",label:"H\xccNH THỨC",dataType:"text",width:"w-32",showInList:!0},{name:"thuTucApDung",label:"THỦ TỤC",dataType:"text",width:"w-28",showInList:!0},{name:"thamPhanPhuTrach",label:"THẨM PH\xc1N",dataType:"text",width:"w-32",showInList:!0},{name:"truongPhoPhongKTNV",label:"TRƯỞNG/PH\xd3 PH\xd2NG",dataType:"text",width:"w-44",showInList:!0},{name:"ghiChu",label:"GHI CH\xda",dataType:"textarea",width:"w-32",showInList:!0},{name:"ghiChuKetQua",label:"GHI CH\xda KQ",dataType:"textarea",width:"w-32",showInList:!0},{name:"trangThaiGiaiQuyet",label:"TRẠNG TH\xc1I",dataType:"select",width:"w-32",showInList:!0}],g=e=>{let{cases:t,onCaseSelect:a,onCaseEdit:n,onCaseDelete:i,onBulkAction:g,onSort:p,currentSort:y,loading:b=!1}=e,{hasPermission:f}=(0,c.S)(),[j,v]=(0,r.useState)([]),[N,w]=(0,r.useState)(!1),[C,T]=(0,r.useState)([]),[D,k]=(0,r.useState)([]),[L,S]=(0,r.useState)(null),[A,P]=(0,r.useState)([]);(0,r.useEffect)(()=>{_()},[]);let _=async()=>{try{let e=localStorage.getItem("sessionToken")||"",[t,a,s]=await Promise.all([d.A.getCustomFields("CourtCase",e),u.A.getFieldConfiguration("CourtCase",e),h.A.getDateCountdowns("CourtCase",e)]);if(t.payload.success&&a.payload.success){T(t.payload.fields),S(a.payload.configuration),s.payload.success&&P(s.payload.countdowns);let e=a.payload.configuration.fieldConfigs,r=[];x.forEach(t=>{let a=e.find(e=>e.fieldName===t.name);if((null==a?void 0:a.showInList)!==!1){var s,l,n;r.push({...t,_id:"default_".concat(t.name),isDefault:!0,config:{showInList:null==(s=null==a?void 0:a.showInList)||s,columnWidth:null!=(l=null==a?void 0:a.columnWidth)?l:150,sortOrder:null!=(n=null==a?void 0:a.sortOrder)?n:0}})}}),t.payload.fields.forEach(t=>{var a,s,l;let n=e.find(e=>e.fieldName===t.name),i=null!=(a=null==n?void 0:n.showInList)?a:t.config.showInList;i&&r.push({...t,isDefault:!1,config:{...t.config,showInList:i,columnWidth:null!=(s=null==n?void 0:n.columnWidth)?s:t.config.columnWidth,sortOrder:null!=(l=null==n?void 0:n.sortOrder)?l:t.config.sortOrder}})}),r.sort((e,t)=>e.config.sortOrder-t.config.sortOrder),k(r)}}catch(e){console.error("Error fetching custom fields:",e)}},q=e=>{let{field:t,children:a}=e,r=(null==y?void 0:y.sortBy)===t,l=r&&(null==y?void 0:y.sortOrder)==="asc",n=r&&(null==y?void 0:y.sortOrder)==="desc";return(0,s.jsxs)("button",{onClick:()=>(e=>{if(!p)return;let t="asc";(null==y?void 0:y.sortBy)===e&&(t="asc"===y.sortOrder?"desc":"asc"),p(e,t)})(t),className:"flex items-center gap-1 hover:text-gray-700 transition-colors",children:[a,(0,s.jsxs)("span",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xs leading-none ".concat(l?"text-blue-600":"text-gray-400"),children:"▲"}),(0,s.jsx)("span",{className:"text-xs leading-none ".concat(n?"text-blue-600":"text-gray-400"),children:"▼"})]})]})},B=(e,t)=>{if(t.isDefault){let a=e[t.name];return"date"===t.dataType&&a?(0,o.Yq)(a):a||""}{var a,s;let r=null==(a=e.customFields)?void 0:a[t.name];if(!r)return"";switch(t.dataType){case"date":return r?(0,o.Yq)(r):"";case"datetime":return r?new Date(r).toLocaleString("vi-VN"):"";case"currency":return new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(r);case"percentage":return"".concat(r,"%");case"boolean":return r?"C\xf3":"Kh\xf4ng";case"select":let l=null==(s=t.config.options)?void 0:s.find(e=>e.value===r);return l?l.label:r;case"multiselect":if(Array.isArray(r))return r.map(e=>{var a;let s=null==(a=t.config.options)?void 0:a.find(t=>t.value===e);return s?s.label:e}).join(", ");return r;default:return r.toString()}}};return b?(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"space-y-4",children:[...Array(8)].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})})}):(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:[(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch vụ việc t\xf2a \xe1n"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Hiển thị ",t.length," vụ việc với ",D.filter(e=>e.config.showInList).length," cột"]})]}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:["Tổng: ",(0,s.jsx)("span",{className:"font-semibold text-gray-900",children:t.length})," vụ việc"]})})]})}),j.length>0&&(0,s.jsx)("div",{className:"bg-blue-50 border-b border-blue-200 px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("span",{className:"text-sm text-blue-700 font-medium",children:["Đ\xe3 chọn ",j.length," vụ việc"]})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>{if(0===j.length)return void l.oR.warning("Vui l\xf2ng chọn \xedt nhất một vụ việc để x\xf3a");confirm("Bạn c\xf3 chắc chắn muốn x\xf3a ".concat(j.length," vụ việc đ\xe3 chọn?"))&&(g(j,"delete"),v([]),w(!1))},className:"flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium",children:"X\xf3a đ\xe3 chọn"}),(0,s.jsx)("button",{onClick:()=>{v([]),w(!1)},className:"flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors",children:"Bỏ chọn"})]})]})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[f("court_case_delete")&&(0,s.jsx)("th",{className:"px-4 py-4 text-left w-12",children:(0,s.jsx)("input",{type:"checkbox",checked:N,onChange:e=>(e=>{w(e),e?v(t.map(e=>e._id)):v([])})(e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),D.filter(e=>e.config.showInList).map(e=>(0,s.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider ".concat(e.width||"w-32"),style:{width:e.config.columnWidth||150},children:p?(0,s.jsx)(q,{field:e.name,children:e.label}):e.label},e._id)),(0,s.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"THAO T\xc1C"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors cursor-pointer",onClick:()=>a(e),children:[f("court_case_delete")&&(0,s.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,s.jsx)("input",{type:"checkbox",checked:j.includes(e._id),onChange:t=>{t.stopPropagation(),((e,t)=>{t?v(t=>[...t,e]):(v(t=>t.filter(t=>t!==e)),w(!1))})(e._id,t.target.checked)},className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),D.filter(e=>e.config.showInList).map(a=>(0,s.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",style:{maxWidth:a.config.columnWidth||150},children:"stt"===a.name?(0,s.jsx)("span",{className:"font-medium",children:t+1}):"trangThaiGiaiQuyet"===a.name?(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ".concat((e=>{switch(e){case"Chưa giải quyết":return"bg-red-100 text-red-800 border-red-200";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(B(e,a))),children:B(e,a)}):(e=>e.isDefault?["ngayNhanVanThu","ngayThuLy","ngayBanHanh"].includes(e.name):"date"===e.dataType||"datetime"===e.dataType)(a)?(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"truncate",title:B(e,a),children:B(e,a)}),(()=>{let t,r=(t=a.name,A.find(e=>e.fieldName===t&&e.countdownConfig.enabled));if(r&&r.countdownConfig.showCountdownBadge){var l;let t=a.isDefault?e[a.name]:null==(l=e.customFields)?void 0:l[a.name];if(t)return(0,s.jsx)(m.A,{date:t,warningDays:r.countdownConfig.warningDays,dangerDays:r.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0})}return null})()]}):(0,s.jsx)("div",{className:"truncate",title:B(e,a),children:B(e,a)})},a._id)),(0,s.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),a(e)},className:"text-blue-600 hover:text-blue-900 transition-colors",title:"Xem chi tiết",children:"\uD83D\uDC41️"}),f("court_case_edit")&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),n(e)},className:"text-green-600 hover:text-green-900 transition-colors",title:"Chỉnh sửa",children:"✏️"}),f("court_case_delete")&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),i(e._id)},className:"text-red-600 hover:text-red-900 transition-colors",title:"X\xf3a",children:"\uD83D\uDDD1️"})]})})]},e._id))})]})}),0===t.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCB"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Kh\xf4ng c\xf3 vụ việc n\xe0o"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Chưa c\xf3 dữ liệu vụ việc t\xf2a \xe1n để hiển thị."})]})]})};var p=a(62177),y=a(63560),b=a(74556);let f=b.k5(["Chưa giải quyết","Đang giải quyết","Đ\xe3 giải quyết"]),j=b.Ik({soVanThu:b.Yj().max(100).optional(),ngayNhanVanThu:b.Yj().optional(),loaiAn:b.Yj().max(100).optional(),soThuLy:b.Yj().max(100).optional(),ngayThuLy:b.Yj().optional(),tand:b.Yj().max(200).optional(),soBanAn:b.Yj().max(100).optional(),ngayBanHanh:b.Yj().optional(),biCaoNguoiKhieuKien:b.Yj().max(500).optional(),toiDanhNoiDung:b.Yj().max(1e3).optional(),quanHePhatLuat:b.Yj().max(500).optional(),hinhThucXuLy:b.Yj().max(200).optional(),thuTucApDung:b.Yj().max(100).optional(),thamPhanPhuTrach:b.Yj().max(200).optional(),truongPhoPhongKTNV:b.Yj().max(200).optional(),ghiChu:b.Yj().max(1e3).optional(),ghiChuKetQua:b.Yj().max(1e3).optional(),trangThaiGiaiQuyet:f.default("Chưa giải quyết"),thoiHan90NgayVoiNgayNhanVanThu:b.Yj().optional(),thoiGianConLaiCuaThoiHan90NgayNhanVanThu:b.ai().optional()});j.partial(),b.Ik({page:b.ai().int().positive().default(1),limit:b.ai().int().positive().max(100).default(20),search:b.Yj().optional(),loaiAn:b.Yj().optional(),trangThaiGiaiQuyet:f.optional(),thuTucApDung:b.Yj().optional(),fromDate:b.Yj().optional(),toDate:b.Yj().optional(),sortBy:b.Yj().default("createdAt"),sortOrder:b.k5(["asc","desc"]).default("desc")});let v=j.extend({_id:b.Yj(),stt:b.ai(),createdAt:b.Yj(),updatedAt:b.Yj(),createdBy:b.Ik({_id:b.Yj(),username:b.Yj(),email:b.Yj().optional()}).optional(),updatedBy:b.Ik({_id:b.Yj(),username:b.Yj(),email:b.Yj().optional()}).optional(),ngayNhanVanThuFormatted:b.Yj().optional(),ngayThuLyFormatted:b.Yj().optional(),ngayBanHanhFormatted:b.Yj().optional(),thoiHan90Ngay:b.Yj().optional(),thoiHan90NgayFormatted:b.Yj().optional(),soNgayConLai:b.ai().optional(),trangThaiThoiHan:b.k5(["Qu\xe1 hạn","Sắp hết hạn","Gần hết hạn","C\xf2n thời gian"]).optional()});b.Ik({success:b.zM(),cases:b.YO(v),pagination:b.Ik({currentPage:b.ai(),totalPages:b.ai(),totalItems:b.ai(),itemsPerPage:b.ai(),hasNextPage:b.zM(),hasPrevPage:b.zM()})}),b.Ik({success:b.zM(),stats:b.Ik({total:b.ai(),byStatus:b.YO(b.Ik({_id:b.Yj(),count:b.ai()})),byType:b.YO(b.Ik({_id:b.Yj(),count:b.ai()})),byProcedure:b.YO(b.Ik({_id:b.Yj(),count:b.ai()}))})});let N=e=>{let{courtCase:t,onSubmit:a,onCancel:n,loading:i=!1}=e,o=!!t,[c,u]=(0,r.useState)([]),[h,m]=(0,r.useState)({}),{register:x,handleSubmit:g,formState:{errors:b},reset:v,setValue:N,watch:w}=(0,p.mN)({resolver:(0,y.u)(j),defaultValues:{trangThaiGiaiQuyet:"Chưa giải quyết"}});(0,r.useEffect)(()=>{C()},[]),(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&n()};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[n]),(0,r.useEffect)(()=>{if(t){let e=t.ngayNhanVanThu?new Date(t.ngayNhanVanThu).toISOString().split("T")[0]:"",a=t.ngayThuLy?new Date(t.ngayThuLy).toISOString().split("T")[0]:"",s=t.ngayBanHanh?new Date(t.ngayBanHanh).toISOString().split("T")[0]:"";v({...t,ngayNhanVanThu:e,ngayThuLy:a,ngayBanHanh:s}),t.customFields&&m(t.customFields)}},[t,v]);let C=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await d.A.getCustomFields("CourtCase",e);t.payload.success&&u(t.payload.fields)}catch(e){console.error("Error fetching custom fields:",e)}},T=(e,t)=>{m(a=>({...a,[e]:t}))};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4",onClick:e=>{e.target===e.currentTarget&&n()},children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden relative",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:o?"✏️":"➕"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:o?"Chỉnh sửa vụ việc":"Th\xeam vụ việc mới"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:o?"Cập nhật th\xf4ng tin vụ việc t\xf2a \xe1n":"Nhập đầy đủ th\xf4ng tin vụ việc mới (".concat(c.length+19," trường)")})]})]}),(0,s.jsx)("button",{onClick:n,disabled:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50",children:"✕"})]}),(0,s.jsx)("div",{className:"overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]",children:(0,s.jsxs)("form",{id:"court-case-form",onSubmit:g(e=>{let t=[];if(t.length>0)return void t.forEach(e=>l.oR.error(e));a({...e,customFields:h})}),className:"p-6 space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2",children:"\uD83D\uDCCB Th\xf4ng tin cơ bản"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"soVanThu",className:"block text-sm font-medium text-gray-700 mb-2",children:"Số văn thư"}),(0,s.jsx)("input",{id:"soVanThu",type:"text",...x("soVanThu"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập số văn thư"}),b.soVanThu&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.soVanThu.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"ngayNhanVanThu",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xe0y nhận văn thư"}),(0,s.jsx)("input",{id:"ngayNhanVanThu",type:"date",...x("ngayNhanVanThu"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),b.ngayNhanVanThu&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.ngayNhanVanThu.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"loaiAn",className:"block text-sm font-medium text-gray-700 mb-2",children:"Loại \xe1n"}),(0,s.jsx)("input",{id:"loaiAn",type:"text",...x("loaiAn"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập loại \xe1n"}),b.loaiAn&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.loaiAn.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"soThuLy",className:"block text-sm font-medium text-gray-700 mb-2",children:"Số thụ l\xfd"}),(0,s.jsx)("input",{id:"soThuLy",type:"text",...x("soThuLy"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập số thụ l\xfd"}),b.soThuLy&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.soThuLy.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"ngayThuLy",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xe0y thụ l\xfd"}),(0,s.jsx)("input",{id:"ngayThuLy",type:"date",...x("ngayThuLy"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),b.ngayThuLy&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.ngayThuLy.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"tand",className:"block text-sm font-medium text-gray-700 mb-2",children:"TAND"}),(0,s.jsx)("input",{id:"tand",type:"text",...x("tand"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập TAND"}),b.tand&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.tand.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"soBanAn",className:"block text-sm font-medium text-gray-700 mb-2",children:"Số bản \xe1n"}),(0,s.jsx)("input",{id:"soBanAn",type:"text",...x("soBanAn"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập số bản \xe1n"}),b.soBanAn&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.soBanAn.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"ngayBanHanh",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xe0y ban h\xe0nh"}),(0,s.jsx)("input",{id:"ngayBanHanh",type:"date",...x("ngayBanHanh"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),b.ngayBanHanh&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.ngayBanHanh.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"trangThaiGiaiQuyet",className:"block text-sm font-medium text-gray-700 mb-2",children:["Trạng th\xe1i giải quyết ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("select",{id:"trangThaiGiaiQuyet",...x("trangThaiGiaiQuyet"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:f.options.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),b.trangThaiGiaiQuyet&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.trangThaiGiaiQuyet.message})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2",children:"\uD83D\uDCDD Th\xf4ng tin chi tiết"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"biCaoNguoiKhieuKien",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bị c\xe1o/Người khiếu kiện"}),(0,s.jsx)("textarea",{id:"biCaoNguoiKhieuKien",...x("biCaoNguoiKhieuKien"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập th\xf4ng tin bị c\xe1o/người khiếu kiện"}),b.biCaoNguoiKhieuKien&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.biCaoNguoiKhieuKien.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"toiDanhNoiDung",className:"block text-sm font-medium text-gray-700 mb-2",children:"Tội danh/Nội dung"}),(0,s.jsx)("textarea",{id:"toiDanhNoiDung",...x("toiDanhNoiDung"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập tội danh/nội dung"}),b.toiDanhNoiDung&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.toiDanhNoiDung.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"quanHePhatLuat",className:"block text-sm font-medium text-gray-700 mb-2",children:"Quan hệ ph\xe1p luật"}),(0,s.jsx)("input",{id:"quanHePhatLuat",type:"text",...x("quanHePhatLuat"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập quan hệ ph\xe1p luật"}),b.quanHePhatLuat&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.quanHePhatLuat.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"hinhThucXuLy",className:"block text-sm font-medium text-gray-700 mb-2",children:"H\xecnh thức xử l\xfd"}),(0,s.jsx)("input",{id:"hinhThucXuLy",type:"text",...x("hinhThucXuLy"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập h\xecnh thức xử l\xfd"}),b.hinhThucXuLy&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.hinhThucXuLy.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"thuTucApDung",className:"block text-sm font-medium text-gray-700 mb-2",children:"Thủ tục \xe1p dụng"}),(0,s.jsx)("input",{id:"thuTucApDung",type:"text",...x("thuTucApDung"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập thủ tục \xe1p dụng"}),b.thuTucApDung&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.thuTucApDung.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"thamPhanPhuTrach",className:"block text-sm font-medium text-gray-700 mb-2",children:"Thẩm ph\xe1n phụ tr\xe1ch"}),(0,s.jsx)("input",{id:"thamPhanPhuTrach",type:"text",...x("thamPhanPhuTrach"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập thẩm ph\xe1n phụ tr\xe1ch"}),b.thamPhanPhuTrach&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.thamPhanPhuTrach.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"truongPhoPhongKTNV",className:"block text-sm font-medium text-gray-700 mb-2",children:"Trưởng/Ph\xf3 ph\xf2ng KTNV"}),(0,s.jsx)("input",{id:"truongPhoPhongKTNV",type:"text",...x("truongPhoPhongKTNV"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập trưởng/ph\xf3 ph\xf2ng KTNV"}),b.truongPhoPhongKTNV&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.truongPhoPhongKTNV.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"ghiChu",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ghi ch\xfa"}),(0,s.jsx)("textarea",{id:"ghiChu",...x("ghiChu"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập ghi ch\xfa"}),b.ghiChu&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.ghiChu.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"ghiChuKetQua",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ghi ch\xfa kết quả"}),(0,s.jsx)("textarea",{id:"ghiChuKetQua",...x("ghiChuKetQua"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập ghi ch\xfa kết quả"}),b.ghiChuKetQua&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b.ghiChuKetQua.message})]})]})]}),c.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2",children:"⚙️ Th\xf4ng tin bổ sung"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:c.map(e=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"custom_".concat(e.name),className:"block text-sm font-medium text-gray-700 mb-2",children:[e.label,e.config.required&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(e=>{var t,a;let r=h[e.name]||e.config.defaultValue||"",l="custom_".concat(e.name);switch(e.dataType){case"text":case"email":case"phone":case"url":return(0,s.jsx)("input",{id:l,type:"email"===e.dataType?"email":"phone"===e.dataType?"tel":"url"===e.dataType?"url":"text",value:r,onChange:t=>T(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,maxLength:e.config.maxLength,minLength:e.config.minLength,pattern:e.config.pattern});case"textarea":return(0,s.jsx)("textarea",{id:l,value:r,onChange:t=>T(e.name,t.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,maxLength:e.config.maxLength,minLength:e.config.minLength});case"number":case"currency":case"percentage":return(0,s.jsx)("input",{id:l,type:"number",value:r,onChange:t=>T(e.name,parseFloat(t.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,min:e.config.min,max:e.config.max,step:e.config.decimals?"0.".concat("0".repeat(e.config.decimals-1),"1"):"1"});case"date":return(0,s.jsx)("input",{id:l,type:"date",value:r?new Date(r).toISOString().split("T")[0]:"",onChange:t=>T(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:e.config.required,min:e.config.minDate?new Date(e.config.minDate).toISOString().split("T")[0]:void 0,max:e.config.maxDate?new Date(e.config.maxDate).toISOString().split("T")[0]:void 0});case"datetime":return(0,s.jsx)("input",{id:l,type:"datetime-local",value:r?new Date(r).toISOString().slice(0,16):"",onChange:t=>T(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:e.config.required});case"boolean":return(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"radio",name:l,checked:!0===r,onChange:()=>T(e.name,!0),className:"text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{children:"C\xf3"})]}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"radio",name:l,checked:!1===r,onChange:()=>T(e.name,!1),className:"text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{children:"Kh\xf4ng"})]})]});case"select":return(0,s.jsxs)("select",{id:l,value:r,onChange:t=>T(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:e.config.required,children:[(0,s.jsxs)("option",{value:"",children:["-- Chọn ",e.label.toLowerCase()," --"]}),null==(t=e.config.options)?void 0:t.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]});case"multiselect":return(0,s.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2",children:null==(a=e.config.options)?void 0:a.map(t=>(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:Array.isArray(r)&&r.includes(t.value),onChange:a=>{let s=Array.isArray(r)?r:[];a.target.checked?T(e.name,[...s,t.value]):T(e.name,s.filter(e=>e!==t.value))},className:"text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:t.label})]},t.value))});default:return(0,s.jsx)("input",{id:l,type:"text",value:r,onChange:t=>T(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required})}})(e),e.description&&(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:e.description})]},e._id))})]})]})}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200 bg-gray-50",children:[(0,s.jsx)("button",{type:"button",onClick:n,className:"px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsx)("button",{type:"submit",form:"court-case-form",disabled:i,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Đang xử l\xfd...":o?"Cập nhật":"Tạo mới"})]}),i&&(0,s.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-3",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:o?"Đang cập nhật...":"Đang tạo vụ việc..."})]})})]})})},w=e=>{let{courtCase:t,onClose:a,onEdit:n,onDelete:i}=e,{hasPermission:x}=(0,c.S)(),[g,p]=(0,r.useState)([]),[y,b]=(0,r.useState)([]),[f,j]=(0,r.useState)(!0),v=[{name:"stt",label:"STT",dataType:"number",required:!0},{name:"soVanThu",label:"Số văn thư",dataType:"text",required:!1},{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư",dataType:"date",required:!1},{name:"loaiAn",label:"Loại \xe1n",dataType:"text",required:!1},{name:"soThuLy",label:"Số thụ l\xfd",dataType:"text",required:!1},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd",dataType:"date",required:!1},{name:"tand",label:"TAND",dataType:"text",required:!1},{name:"soBanAn",label:"Số bản \xe1n/quyết định",dataType:"text",required:!1},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh",dataType:"date",required:!1},{name:"biCaoNguoiKhieuKien",label:"Bị c\xe1o/Nguy\xean đơn/Người khiếu kiện",dataType:"textarea",required:!1},{name:"toiDanhNoiDung",label:"Tội danh/Bồi dưỡng/Nội dung khiếu kiện",dataType:"textarea",required:!1},{name:"quanHePhatLuat",label:"Tội danh/Quan hệ ph\xe1p luật",dataType:"textarea",required:!1},{name:"hinhThucXuLy",label:"H\xecnh thức xử l\xfd",dataType:"text",required:!1},{name:"thuTucApDung",label:"Thủ tục \xe1p dụng",dataType:"text",required:!1},{name:"thamPhanPhuTrach",label:"Thẩm ph\xe1n phụ tr\xe1ch",dataType:"text",required:!1},{name:"truongPhoPhongKTNV",label:"Trưởng/Ph\xf3 ph\xf2ng KTNV/Thẩm tra vi\xean",dataType:"text",required:!1},{name:"trangThaiGiaiQuyet",label:"Trạng th\xe1i giải quyết",dataType:"select",required:!1},{name:"ghiChu",label:"Ghi ch\xfa",dataType:"textarea",required:!1},{name:"ghiChuKetQua",label:"Ghi ch\xfa kết quả",dataType:"textarea",required:!1}];(0,r.useEffect)(()=>{N()},[]);let N=async()=>{try{j(!0);let e=localStorage.getItem("sessionToken")||"",[t,a,s]=await Promise.all([d.A.getCustomFields("CourtCase",e),u.A.getFieldConfiguration("CourtCase",e),h.A.getDateCountdowns("CourtCase",e)]);if(t.payload.success&&a.payload.success){let e=a.payload.configuration.fieldConfigs,s=[];v.forEach(t=>{var a,r,l,n;let i=e.find(e=>e.fieldName===t.name);s.push({...t,_id:"default_".concat(t.name),isDefault:!0,config:{showInList:null==(a=null==i?void 0:i.showInList)||a,showInDetail:null==(r=null==i?void 0:i.showInDetail)||r,columnWidth:null!=(l=null==i?void 0:i.columnWidth)?l:150,sortOrder:null!=(n=null==i?void 0:i.sortOrder)?n:0,required:t.required,options:t.options||[]}})}),t.payload.fields.forEach(t=>{var a,r,l;let n=e.find(e=>e.fieldName===t.name);s.push({...t,isDefault:!1,config:{...t.config,showInDetail:null==(a=null==n?void 0:n.showInDetail)||a,columnWidth:null!=(r=null==n?void 0:n.columnWidth)?r:t.config.columnWidth,sortOrder:null!=(l=null==n?void 0:n.sortOrder)?l:t.config.sortOrder}})}),s.sort((e,t)=>(e.config.sortOrder||0)-(t.config.sortOrder||0)),p(s)}s.payload.success&&b(s.payload.countdowns)}catch(e){console.error("Error fetching fields and countdowns:",e),l.oR.error("C\xf3 lỗi xảy ra khi tải th\xf4ng tin trường")}finally{j(!1)}};return(0,s.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,s.jsxs)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-3xl mr-3",children:(e=>{if(!e)return"\uD83D\uDCC4";let t=e.toLowerCase();return t.includes("h\xecnh sự")?"⚖️":t.includes("d\xe2n sự")?"\uD83C\uDFE0":t.includes("h\xe0nh ch\xednh")?"\uD83C\uDFDB️":t.includes("kinh tế")?"\uD83D\uDCBC":t.includes("lao động")?"\uD83D\uDC77":"\uD83D\uDCCB"})(t.loaiAn)}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:["Chi tiết vụ việc #",t.stt]}),(0,s.jsx)("p",{className:"text-gray-600",children:t.soThuLy})]})]}),(0,s.jsx)("button",{onClick:a,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),f?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Đang tải th\xf4ng tin..."})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDCCB Th\xf4ng tin chi tiết"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:g.filter(e=>!1!==e.config.showInDetail).map(e=>{let a,r=(e=>{var a;if(e.isDefault){let a=t[e.name];return"date"===e.dataType&&a?(0,o.Yq)(a):a||""}return(null==(a=t.customFields)?void 0:a[e.name])||""})(e),l=(e=>"date"===e.dataType||"datetime"===e.dataType)(e)?(a=e.name,y.find(e=>e.fieldName===a&&e.countdownConfig.enabled)):null;return(0,s.jsxs)("div",{className:"textarea"===e.dataType?"md:col-span-2":"",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:[e.label,e.isDefault&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-blue-100 text-blue-600 rounded",children:"Cơ bản"}),!e.isDefault&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-600 rounded",children:"T\xf9y chỉnh"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"flex-1",style:{color:"#111827"},children:((e,t)=>{if(!t&&0!==t)return(0,s.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 dữ liệu"});switch(e.dataType){case"select":if("trangThaiGiaiQuyet"===e.name)return(0,s.jsx)("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full ".concat((e=>{switch(e){case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800";case"Chưa giải quyết":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(t)),children:t});return(0,s.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:t});case"textarea":return(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:t});case"number":return(0,s.jsx)("span",{className:"font-mono",children:t.toLocaleString()});case"date":default:return(0,s.jsx)("span",{children:t});case"boolean":return t?"✅ C\xf3":"❌ Kh\xf4ng"}})(e,r)}),l&&r&&(0,s.jsx)(m.A,{date:r,warningDays:l.countdownConfig.warningDays,dangerDays:l.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0})]})]},e._id)})})]}),(0,s.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"ℹ️ Th\xf4ng tin hệ thống"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.createdBy&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người tạo"}),(0,s.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:t.createdBy.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y tạo"}),(0,s.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,o.Yq)(t.createdAt)})]})]}),t.updatedBy&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người cập nhật cuối"}),(0,s.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:t.updatedBy.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y cập nhật cuối"}),(0,s.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,o.Yq)(t.updatedAt)})]})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:[(0,s.jsx)("button",{onClick:a,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"}),x("court_case_edit")&&(0,s.jsx)("button",{onClick:()=>{a(),n(t)},className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#059669",color:"#ffffff"},children:"Chỉnh sửa"}),x("court_case_delete")&&(0,s.jsx)("button",{onClick:()=>{confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?")&&(i(t._id),a())},className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"X\xf3a"})]})]})})};var C=a(54624),T=a(74466),D=a(59434);let k=(0,T.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),L=r.forwardRef((e,t)=>{let{className:a,variant:r,size:l,asChild:n=!1,...i}=e,o=n?C.DX:"button";return(0,s.jsx)(o,{className:(0,D.cn)(k({variant:r,size:l,className:a})),ref:t,...i})});L.displayName="Button";var S=a(17703),A=a(52814),P=a(40646),_=a(1243),q=a(54861),B=a(54416),I=a(92657);let R=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),E=e=>{let{preview:t,onConfirmImport:a,onCancel:l,isImporting:n}=e,[i,o]=(0,r.useState)(!1),[c,d]=(0,r.useState)("all"),u="all"===c?t.data:t.data.filter(e=>e.status===c);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Xem trước dữ liệu import"}),(0,s.jsxs)(L,{variant:"outline",onClick:l,className:"text-gray-600 hover:text-gray-800",children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Đ\xf3ng"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)(S.Zp,{children:[(0,s.jsx)(S.aR,{className:"pb-2",children:(0,s.jsx)(S.ZB,{className:"text-sm font-medium text-gray-600",children:"Tổng số d\xf2ng"})}),(0,s.jsx)(S.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.summary.totalRows})})]}),(0,s.jsxs)(S.Zp,{children:[(0,s.jsx)(S.aR,{className:"pb-2",children:(0,s.jsx)(S.ZB,{className:"text-sm font-medium text-gray-600",children:"Hợp lệ"})}),(0,s.jsx)(S.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.summary.validRows})})]}),(0,s.jsxs)(S.Zp,{children:[(0,s.jsx)(S.aR,{className:"pb-2",children:(0,s.jsx)(S.ZB,{className:"text-sm font-medium text-gray-600",children:"Cảnh b\xe1o"})}),(0,s.jsx)(S.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.summary.warningRows})})]}),(0,s.jsxs)(S.Zp,{children:[(0,s.jsx)(S.aR,{className:"pb-2",children:(0,s.jsx)(S.ZB,{className:"text-sm font-medium text-gray-600",children:"Lỗi"})}),(0,s.jsx)(S.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.summary.errorRows})})]})]}),t.warnings.length>0&&(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(_.A,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-yellow-800 mb-2",children:"Cảnh b\xe1o:"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:t.warnings.map((e,t)=>(0,s.jsx)("li",{className:"text-sm",children:e},t))})]})]})}),t.errors.length>0&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(q.A,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-red-800 mb-2",children:"Lỗi:"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-1 text-red-700",children:t.errors.map((e,t)=>(0,s.jsx)("li",{className:"text-sm",children:e},t))})]})]})}),(0,s.jsx)(S.Zp,{children:(0,s.jsx)(S.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:t.summary.canImport?(0,s.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,s.jsx)(P.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:"C\xf3 thể import được"})]}):(0,s.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,s.jsx)(q.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:"Kh\xf4ng thể import do c\xf3 lỗi"})]})}),(0,s.jsxs)("div",{className:"space-x-2",children:[(0,s.jsxs)(L,{variant:"outline",onClick:()=>o(!i),children:[(0,s.jsx)(I.A,{className:"h-4 w-4 mr-2"}),i?"Ẩn chi tiết":"Xem chi tiết"]}),(0,s.jsxs)(L,{onClick:a,disabled:!t.summary.canImport||n,className:"bg-blue-600 hover:bg-blue-700",children:[(0,s.jsx)(R,{className:"h-4 w-4 mr-2"}),n?"Đang import...":"X\xe1c nhận import"]})]})]})})}),i&&(0,s.jsxs)(S.Zp,{children:[(0,s.jsx)(S.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(S.ZB,{children:"Chi tiết dữ liệu"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(L,{variant:"all"===c?"default":"outline",size:"sm",onClick:()=>d("all"),children:["Tất cả (",t.summary.totalRows,")"]}),(0,s.jsxs)(L,{variant:"valid"===c?"default":"outline",size:"sm",onClick:()=>d("valid"),children:["Hợp lệ (",t.summary.validRows,")"]}),(0,s.jsxs)(L,{variant:"warning"===c?"default":"outline",size:"sm",onClick:()=>d("warning"),children:["Cảnh b\xe1o (",t.summary.warningRows,")"]}),(0,s.jsxs)(L,{variant:"error"===c?"default":"outline",size:"sm",onClick:()=>d("error"),children:["Lỗi (",t.summary.errorRows,")"]})]})]})}),(0,s.jsx)(S.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-gray-50",children:[(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"D\xf2ng"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Trạng th\xe1i"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Số thụ l\xfd"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Loại \xe1n"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Ng\xe0y thụ l\xfd"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Bị c\xe1o/NĐ/NKK"}),(0,s.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Lỗi/Cảnh b\xe1o"})]})}),(0,s.jsx)("tbody",{children:u.map((e,t)=>(0,s.jsxs)("tr",{className:"\n                      ".concat("error"===e.status?"bg-red-50":"warning"===e.status?"bg-yellow-50":"bg-green-50","\n                    "),children:[(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.rowNumber}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"valid":return(0,s.jsx)(P.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,s.jsx)(_.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,s.jsx)(q.A,{className:"h-4 w-4 text-red-500"});default:return null}})(e.status),(0,s.jsx)("span",{className:"ml-2",children:(e=>{switch(e){case"valid":return(0,s.jsx)(A.Ex,{variant:"success",children:"Hợp lệ"});case"warning":return(0,s.jsx)(A.Ex,{variant:"warning",children:"Cảnh b\xe1o"});case"error":return(0,s.jsx)(A.Ex,{variant:"danger",children:"Lỗi"});default:return null}})(e.status)})]})}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.soThuLy}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.loaiAn}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.ngayThuLyDisplay||""}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2 max-w-xs truncate",children:e.biCaoNguoiKhieuKien}),(0,s.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.validationErrors.length>0&&(0,s.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:e.validationErrors.map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t))})})]},t))})]})})})]})]})},K=e=>{let{onImportComplete:t,onClose:a}=e,[n,o]=(0,r.useState)(null),[c,d]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null),[g,p]=(0,r.useState)(null),y=(0,r.useRef)(null),b=async()=>{if(!n)return void l.oR.error("Vui l\xf2ng chọn file Excel");try{h(!0);let e=await i.previewImport(n);e.payload.success?x(e.payload.preview):l.oR.error("Kh\xf4ng thể xem trước file")}catch(e){console.error("Error previewing:",e),l.oR.error("C\xf3 lỗi xảy ra khi xem trước file")}finally{h(!1)}},f=async()=>{if(!n)return void l.oR.error("Vui l\xf2ng chọn file Excel");try{d(!0);let e=await i.importCourtCases(n);e.payload.success?(p(e.payload.results),x(null),l.oR.success("Import th\xe0nh c\xf4ng! ".concat(e.payload.results.success,"/").concat(e.payload.results.total," vụ việc")),t()):l.oR.error("Import thất bại")}catch(e){console.error("Error importing:",e),l.oR.error("C\xf3 lỗi xảy ra khi import file")}finally{d(!1)}},j=async()=>{try{await i.downloadTemplate(),l.oR.success("Đ\xe3 tải xuống file mẫu")}catch(e){console.error("Error downloading template:",e),l.oR.error("Kh\xf4ng thể tải xuống file mẫu")}};return(0,s.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,s.jsx)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:m?(0,s.jsx)(E,{preview:m,onConfirmImport:f,onCancel:()=>x(null),isImporting:c}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCE5 Import vụ việc từ Excel"}),(0,s.jsx)("button",{onClick:a,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Hướng dẫn:"}),(0,s.jsxs)("ul",{className:"text-sm space-y-1",style:{color:"#111827"},children:[(0,s.jsx)("li",{children:"• File Excel phải c\xf3 định dạng .xlsx hoặc .xls"}),(0,s.jsx)("li",{children:"• D\xf2ng đầu ti\xean l\xe0 ti\xeau đề cột (sẽ bị bỏ qua)"}),(0,s.jsx)("li",{children:"• STT c\xf3 thể để trống (hệ thống tự tạo)"}),(0,s.jsx)("li",{children:"• Ng\xe0y th\xe1ng theo định dạng YYYY-MM-DD hoặc DD/MM/YYYY"}),(0,s.jsx)("li",{children:"• C\xe1c trường kh\xe1c c\xf3 thể để trống"}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Khuyến kh\xedch xem trước dữ liệu trước khi import"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCC4 File mẫu"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tham khảo định dạng"})]}),(0,s.jsx)("button",{onClick:j,className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Tải file mẫu"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Chọn file Excel"}),(0,s.jsx)("input",{ref:y,type:"file",accept:".xlsx,.xls",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){if(!a.name.endsWith(".xlsx")&&!a.name.endsWith(".xls"))return void l.oR.error("Vui l\xf2ng chọn file Excel (.xlsx hoặc .xls)");o(a),p(null),x(null)}},className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),n&&(0,s.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("p",{className:"text-sm",style:{color:"#111827"},children:[(0,s.jsx)("strong",{children:"File đ\xe3 chọn:"})," ",n.name]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["K\xedch thước: ",(n.size/1024).toFixed(2)," KB"]})]})]}),g&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCCA Kết quả import:"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:g.total}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng số d\xf2ng"})]}),(0,s.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.success}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Th\xe0nh c\xf4ng"})]}),(0,s.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:g.duplicates}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Tr\xf9ng lặp"})]}),(0,s.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:g.errors.length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Lỗi"})]})]}),g.errors.length>0&&(0,s.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,s.jsx)("h5",{className:"font-medium text-red-800 mb-2",children:"❌ Lỗi chi tiết:"}),(0,s.jsx)("div",{className:"max-h-32 overflow-y-auto",children:g.errors.map((e,t)=>(0,s.jsxs)("p",{className:"text-sm text-red-700",children:["• ",e]},t))})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,s.jsx)("button",{onClick:a,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},disabled:c||u,children:"Đ\xf3ng"}),(0,s.jsx)("button",{onClick:b,disabled:!n||c||u,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#059669",color:"#ffffff"},children:u?"Đang xem trước...":"\uD83D\uDC41️ Xem trước"}),(0,s.jsx)("button",{onClick:f,disabled:!n||c||u,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:c?"Đang import...":"\uD83D\uDCE5 Import trực tiếp"})]})]})]})})})},Y=e=>{let{onClose:t}=e,[a,n]=(0,r.useState)(null),[o,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)({fromDate:"",toDate:"",groupBy:"month"}),h=async()=>{try{c(!0);let e=await i.getDetailedStats(d);e.payload.success?n(e.payload.stats):l.oR.error("Kh\xf4ng thể tải thống k\xea chi tiết")}catch(e){console.error("Error fetching detailed stats:",e),l.oR.error("C\xf3 lỗi xảy ra khi tải thống k\xea")}finally{c(!1)}};(0,r.useEffect)(()=>{h()},[d]);let m=(e,t)=>{u(a=>({...a,[e]:t}))},x=async()=>{try{l.oR.info("Đang xuất b\xe1o c\xe1o chi tiết...");let e=await i.exportCourtCases(d),t=new Blob([e.payload],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="bao-cao-chi-tiet-vu-viec-".concat(new Date().toISOString().split("T")[0],".xlsx"),document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(a),l.oR.success("Xuất b\xe1o c\xe1o th\xe0nh c\xf4ng")}catch(e){console.error("Error exporting detailed report:",e),l.oR.error("C\xf3 lỗi xảy ra khi xuất b\xe1o c\xe1o")}};return(0,s.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,s.jsxs)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCCA Thống k\xea chi tiết vụ việc"}),(0,s.jsx)("button",{onClick:t,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDD0D Bộ lọc"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Từ ng\xe0y"}),(0,s.jsx)("input",{type:"date",value:d.fromDate,onChange:e=>m("fromDate",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Đến ng\xe0y"}),(0,s.jsx)("input",{type:"date",value:d.toDate,onChange:e=>m("toDate",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Nh\xf3m theo"}),(0,s.jsxs)("select",{value:d.groupBy,onChange:e=>m("groupBy",e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},children:[(0,s.jsx)("option",{value:"day",children:"Ng\xe0y"}),(0,s.jsx)("option",{value:"week",children:"Tuần"}),(0,s.jsx)("option",{value:"month",children:"Th\xe1ng"}),(0,s.jsx)("option",{value:"quarter",children:"Qu\xfd"}),(0,s.jsx)("option",{value:"year",children:"Năm"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:x,className:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"\uD83D\uDCCA Xuất b\xe1o c\xe1o"})})]})]}),o?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Đang tải thống k\xea..."})]}):a?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Tổng quan"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Tổng số vụ việc:"}),(0,s.jsx)("span",{className:"ml-2 font-bold text-2xl text-blue-600",children:a.summary.total})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Thời gian:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:a.summary.period})]})]})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDCC8 Ph\xe2n bố theo trạng th\xe1i"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:a.byStatus.map(e=>(0,s.jsxs)("div",{className:"p-4 rounded-lg ".concat((e=>{switch(e){case"Đ\xe3 giải quyết":return"text-green-600 bg-green-100";case"Đang giải quyết":return"text-yellow-600 bg-yellow-100";case"Chưa giải quyết":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e._id)),children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.count}),(0,s.jsx)("div",{className:"text-sm",children:e._id||"Kh\xf4ng x\xe1c định"})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"⚖️ Ph\xe2n bố theo loại \xe1n"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:a.byType.map(e=>(0,s.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-gray-700",children:e.count}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:e._id||"Kh\xe1c"})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDD28 Ph\xe2n bố theo h\xecnh thức xử l\xfd"}),(0,s.jsx)("div",{className:"space-y-2",children:a.byProcessingMethod.slice(0,8).map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,s.jsx)("span",{className:"text-sm",children:e._id||"Kh\xf4ng x\xe1c định"}),(0,s.jsx)("span",{className:"font-bold text-blue-600",children:e.count})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDC68‍⚖️ Ph\xe2n bố theo Thẩm ph\xe1n"}),(0,s.jsx)("div",{className:"space-y-2",children:a.topJudges.slice(0,10).map((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-6 h-6 bg-blue-600 text-white rounded-full text-xs flex items-center justify-center mr-3",children:t+1}),(0,s.jsx)("span",{className:"text-sm",children:e._id})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-bold text-blue-600",children:[e.count," vụ"]}),(0,s.jsxs)("div",{className:"text-xs text-green-600",children:[e.resolved," đ\xe3 giải quyết"]})]})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83C\uDFDB️ Ph\xe2n bố theo t\xf2a \xe1n"}),(0,s.jsx)("div",{className:"space-y-2",children:a.byCourt.slice(0,10).map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,s.jsx)("span",{className:"text-sm",children:e._id||"Kh\xf4ng x\xe1c định"}),(0,s.jsx)("span",{className:"font-bold text-purple-600",children:e.count})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDCC8 Xu hướng theo thời gian"}),(0,s.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:a.trends.map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:e._id}),(0,s.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,s.jsxs)("span",{className:"text-blue-600",children:["Tổng: ",e.count]}),(0,s.jsxs)("span",{className:"text-green-600",children:["Đ\xe3 giải quyết: ",e.resolved]}),(0,s.jsxs)("span",{className:"text-yellow-600",children:["Đang giải quyết: ",e.inProgress]}),(0,s.jsxs)("span",{className:"text-red-600",children:["Chưa giải quyết: ",e.pending]})]})]},e._id))})]})]}):(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Kh\xf4ng c\xf3 dữ liệu thống k\xea"}),(0,s.jsx)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:(0,s.jsx)("button",{onClick:t,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"})})]})})},H=e=>{let{searchParams:t,onSearch:a,onReset:l}=e,[n,i]=(0,r.useState)(!1),o=Object.values({search:t.search,loaiAn:t.loaiAn,trangThaiGiaiQuyet:t.trangThaiGiaiQuyet,thuTucApDung:t.thuTucApDung,fromDate:t.fromDate,toDate:t.toDate}).filter(e=>e&&""!==e).length;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>{i(!n)},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"text-xl",children:"\uD83D\uDD0D"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Bộ lọc v\xe0 t\xecm kiếm"}),o>0&&(0,s.jsxs)("p",{className:"text-sm text-blue-600",children:[o," bộ lọc đang được \xe1p dụng"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[!n&&o>0&&(0,s.jsx)("button",{onClick:e=>{e.stopPropagation(),l()},className:"px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors",children:"X\xf3a tất cả"}),(0,s.jsx)("div",{className:"transform transition-transform duration-200 ".concat(n?"rotate-180":""),children:(0,s.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,s.jsx)("div",{className:"transition-all duration-300 ease-in-out ".concat(n?"max-h-96 opacity-100":"max-h-0 opacity-0 overflow-hidden"),children:(0,s.jsx)("div",{className:"px-4 pb-4 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xecm kiếm"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:t.search||"",onChange:e=>a({search:e.target.value}),placeholder:"Số thụ l\xfd, bản \xe1n, t\xean...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDD0D"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Loại \xe1n"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:t.loaiAn||"",onChange:e=>a({loaiAn:e.target.value}),placeholder:"Nhập loại \xe1n để t\xecm kiếm...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"⚖️"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trạng th\xe1i"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("select",{value:t.trangThaiGiaiQuyet||"",onChange:e=>a({trangThaiGiaiQuyet:e.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white",children:[(0,s.jsx)("option",{value:"",children:"Tất cả"}),f.options.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCCA"}),(0,s.jsx)("div",{className:"absolute right-2 top-2.5 text-gray-400 pointer-events-none",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Thủ tục \xe1p dụng"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:t.thuTucApDung||"",onChange:e=>a({thuTucApDung:e.target.value}),placeholder:"Nhập thủ tục \xe1p dụng để t\xecm kiếm...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCDD"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Từ ng\xe0y"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"date",value:t.fromDate||"",onChange:e=>a({fromDate:e.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCC5"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Đến ng\xe0y"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"date",value:t.toDate||"",onChange:e=>a({toDate:e.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCC5"})]})]}),(0,s.jsx)("div",{className:"flex items-end gap-2",children:(0,s.jsx)("button",{onClick:l,className:"flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors",children:"\uD83D\uDDD1️ X\xf3a bộ lọc"})})]})})})]})};var F=a(87708);let O=()=>{let{hasPermission:e}=(0,c.S)(),[t,a]=(0,r.useState)([]),[n,o]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[h,m]=(0,r.useState)(null),[x,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!1),[v,C]=(0,r.useState)(null),[T,D]=(0,r.useState)({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"}),[k,L]=(0,r.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:20,hasNextPage:!1,hasPrevPage:!1}),S=async()=>{try{o(!0);let e=await i.getCourtCases(T);e.payload.success?(a(e.payload.cases),L(e.payload.pagination)):l.oR.error("Kh\xf4ng thể tải danh s\xe1ch vụ việc")}catch(e){console.error("Error fetching court cases:",e),l.oR.error("C\xf3 lỗi xảy ra khi tải danh s\xe1ch vụ việc")}finally{o(!1)}},A=async()=>{if(e("court_case_stats_view"))try{let e=await i.getCourtCaseStats();e.payload.success&&C(e.payload.stats)}catch(e){console.error("Error fetching stats:",e)}};(0,r.useEffect)(()=>{S(),A()},[T]);let P=e=>{D(t=>({...t,page:e}))},_=async e=>{try{o(!0),(await i.createCourtCase(e)).payload.success?(l.oR.success("Th\xeam vụ việc th\xe0nh c\xf4ng"),p(!1),S(),A()):l.oR.error("Kh\xf4ng thể th\xeam vụ việc")}catch(e){var t;console.error("Error creating court case:",e),l.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi th\xeam vụ việc")}finally{o(!1)}},q=async e=>{if(h)try{o(!0),(await i.updateCourtCase(h._id,e)).payload.success?(l.oR.success("Cập nhật vụ việc th\xe0nh c\xf4ng"),m(null),S(),A()):l.oR.error("Kh\xf4ng thể cập nhật vụ việc")}catch(e){var t;console.error("Error updating court case:",e),l.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi cập nhật vụ việc")}finally{o(!1)}},B=async e=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?"))try{(await i.deleteCourtCase(e)).payload.success?(l.oR.success("X\xf3a vụ việc th\xe0nh c\xf4ng"),S(),A()):l.oR.error("Kh\xf4ng thể x\xf3a vụ việc")}catch(e){var t;console.error("Error deleting court case:",e),l.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi x\xf3a vụ việc")}},I=async(e,t)=>{if("delete"===t)try{await i.bulkDeleteCourtCases(e),l.oR.success("Đ\xe3 x\xf3a ".concat(e.length," vụ việc")),S(),A()}catch(e){console.error("Error bulk deleting:",e),l.oR.error("C\xf3 lỗi xảy ra khi x\xf3a h\xe0ng loạt")}},R=async()=>{try{l.oR.info("Đang xuất file Excel...");let e=await i.exportCourtCases(T),t=new Blob([e.payload],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="danh-sach-vu-viec-".concat(new Date().toISOString().split("T")[0],".xlsx"),document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(a),l.oR.success("Xuất file Excel th\xe0nh c\xf4ng")}catch(e){console.error("Error exporting Excel:",e),l.oR.error("C\xf3 lỗi xảy ra khi xuất file Excel")}};return(0,s.jsx)(F.default,{requiredPermissions:["court_case_view"],children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Quản l\xfd vụ việc t\xf2a \xe1n"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Quản l\xfd danh s\xe1ch thụ l\xfd v\xe0 giải quyết vụ việc đề nghị gi\xe1m đốc thẩm, t\xe1i thẩm"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[e("court_case_view")&&(0,s.jsx)("button",{onClick:()=>window.open("/dashboard/court-cases/custom-fields","_blank"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"⚙️ Quản l\xfd trường"}),e("court_case_stats_view")&&(0,s.jsx)("button",{onClick:()=>j(!0),className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"\uD83D\uDCCA Thống k\xea chi tiết"}),e("court_case_import")&&(0,s.jsx)("button",{onClick:()=>b(!0),className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500",children:"\uD83D\uDCE5 Import Excel"}),e("court_case_export")&&(0,s.jsx)("button",{onClick:R,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500",children:"\uD83D\uDCCA Xuất Excel"}),e("court_case_create")&&(0,s.jsx)("button",{onClick:()=>p(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"➕ Th\xeam vụ việc mới"})]})]}),e("court_case_stats_view")&&v&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"text-3xl mr-4",children:"\uD83D\uDCCA"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tổng số vụ việc"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v.total})]})]})}),v.byStatus.map(e=>(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"text-3xl mr-4",children:{"Chưa giải quyết":"\uD83D\uDD34","Đang giải quyết":"\uD83D\uDFE1","Đ\xe3 giải quyết":"\uD83D\uDFE2"}[e._id]||"\uD83D\uDCCB"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e._id}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.count})]})]})},e._id))]}),(0,s.jsx)(H,{searchParams:T,onSearch:e=>{D(t=>({...t,...e,page:1}))},onReset:()=>{D({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"})}}),(0,s.jsx)(g,{cases:t,onCaseSelect:u,onCaseEdit:m,onCaseDelete:B,onBulkAction:I,onSort:(e,t)=>{D(a=>({...a,sortBy:e,sortOrder:t,page:1}))},currentSort:{sortBy:T.sortBy,sortOrder:T.sortOrder},loading:n}),k.totalPages>1&&(0,s.jsx)("div",{className:"bg-white px-6 py-3 rounded-lg shadow",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Hiển thị ",(k.currentPage-1)*k.itemsPerPage+1," đến"," ",Math.min(k.currentPage*k.itemsPerPage,k.totalItems)," trong tổng số"," ",k.totalItems," vụ việc"]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>P(k.currentPage-1),disabled:!k.hasPrevPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Trước"}),(0,s.jsxs)("span",{className:"px-3 py-2 text-sm text-gray-700",children:["Trang ",k.currentPage," / ",k.totalPages]}),(0,s.jsx)("button",{onClick:()=>P(k.currentPage+1),disabled:!k.hasNextPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Sau"})]})]})}),x&&(0,s.jsx)(N,{onSubmit:_,onCancel:()=>p(!1),loading:n}),h&&(0,s.jsx)(N,{courtCase:h,onSubmit:q,onCancel:()=>m(null),loading:n}),d&&(0,s.jsx)(w,{courtCase:d,onClose:()=>u(null),onEdit:m,onDelete:B}),y&&(0,s.jsx)(K,{onImportComplete:()=>{S(),A()},onClose:()=>b(!1)}),f&&(0,s.jsx)(Y,{onClose:()=>j(!1)})]})})}},52814:(e,t,a)=>{"use strict";a.d(t,{Ex:()=>r,eG:()=>l});var s=a(95155);a(12115);let r=e=>{let{children:t,variant:a="default",size:r="md",className:l="",dot:n=!1}=e;return(0,s.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[a],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[r],"\n        ").concat(l,"\n      "),children:[n&&(0,s.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[a])}),t]})},l=e=>{let{role:t,className:a=""}=e,l={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},n=l[t]||l.user;return(0,s.jsx)(r,{variant:n.variant,className:a,children:n.label})}},87708:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var s=a(95155),r=a(38497),l=a(35695),n=a(12115);function i(e){let{children:t,requiredPermission:a,requiredPermissions:i=[],requireAll:o=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:h,isDepartmentManager:m,isLoading:x}=(0,r.S)(),g=(0,l.useRouter)();if((0,n.useEffect)(()=>{if(!x&&!h)(a?"admin"===a&&!!m||d(a):!(i.length>0)||(o?i.every(e=>d(e)):u(i)))||g.replace(c)},[d,u,h,m,x,a,i,o,c,g]),x)return(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,s.jsx)(s.Fragment,{children:t});return(a?"admin"===a&&!!m||d(a):!(i.length>0)||(o?i.every(e=>d(e)):u(i)))?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,s.jsx)("button",{onClick:()=>g.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},92767:(e,t,a)=>{Promise.resolve().then(a.bind(a,51852))}},e=>{e.O(0,[9268,3235,8543,2182,3602,8441,5964,7358],()=>e(e.s=92767)),_N_E=e.O()}]);