"use strict";exports.id=6090,exports.ids=[6090],exports.modules={56090:(a,b,c)=>{c.d(b,{Kv:()=>f,N4:()=>g});var d=c(43210),e=c(93772);function f(a,b){var c,e,f;return a?"function"==typeof(e=c=a)&&(()=>{let a=Object.getPrototypeOf(e);return a.prototype&&a.prototype.isReactComponent})()||"function"==typeof c||"object"==typeof(f=c)&&"symbol"==typeof f.$$typeof&&["react.memo","react.forward_ref"].includes(f.$$typeof.description)?d.createElement(a,b):a:null}function g(a){let b={state:{},onStateChange:()=>{},renderFallbackValue:null,...a},[c]=d.useState(()=>({current:(0,e.ZR)(b)})),[f,g]=d.useState(()=>c.current.initialState);return c.current.setOptions(b=>({...b,...a,state:{...f,...a.state},onStateChange:b=>{g(b),null==a.onStateChange||a.onStateChange(b)}})),c.current}},93772:(a,b,c)=>{function d(a,b){return"function"==typeof a?a(b):a}function e(a,b){return c=>{b.setState(b=>({...b,[a]:d(c,b[a])}))}}function f(a){return a instanceof Function}function g(a,b,c){let d,e=[];return f=>{let g,h;c.key&&c.debug&&(g=Date.now());let i=a(f);if(!(i.length!==e.length||i.some((a,b)=>e[b]!==a)))return d;if(e=i,c.key&&c.debug&&(h=Date.now()),d=b(...i),null==c||null==c.onChange||c.onChange(d),c.key&&c.debug&&null!=c&&c.debug()){let a=Math.round((Date.now()-g)*100)/100,b=Math.round((Date.now()-h)*100)/100,d=b/16,e=(a,b)=>{for(a=String(a);a.length<b;)a=" "+a;return a};console.info(`%c⏱ ${e(b,5)} /${e(a,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,null==c?void 0:c.key)}return d}}function h(a,b,c,d){return{debug:()=>{var c;return null!=(c=null==a?void 0:a.debugAll)?c:a[b]},key:!1,onChange:d}}c.d(b,{HT:()=>S,ZR:()=>R});let i="debugHeaders";function j(a,b,c){var d;let e={id:null!=(d=c.id)?d:b.id,column:b,index:c.index,isPlaceholder:!!c.isPlaceholder,placeholderId:c.placeholderId,depth:c.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let a=[],b=c=>{c.subHeaders&&c.subHeaders.length&&c.subHeaders.map(b),a.push(c)};return b(e),a},getContext:()=>({table:a,header:e,column:b})};return a._features.forEach(b=>{null==b.createHeader||b.createHeader(e,a)}),e}function k(a,b,c,d){var e,f;let g=0,h=function(a,b){void 0===b&&(b=1),g=Math.max(g,b),a.filter(a=>a.getIsVisible()).forEach(a=>{var c;null!=(c=a.columns)&&c.length&&h(a.columns,b+1)},0)};h(a);let i=[],k=(a,b)=>{let e={depth:b,id:[d,`${b}`].filter(Boolean).join("_"),headers:[]},f=[];a.forEach(a=>{let g,h=[...f].reverse()[0],i=a.column.depth===e.depth,k=!1;if(i&&a.column.parent?g=a.column.parent:(g=a.column,k=!0),h&&(null==h?void 0:h.column)===g)h.subHeaders.push(a);else{let e=j(c,g,{id:[d,b,g.id,null==a?void 0:a.id].filter(Boolean).join("_"),isPlaceholder:k,placeholderId:k?`${f.filter(a=>a.column===g).length}`:void 0,depth:b,index:f.length});e.subHeaders.push(a),f.push(e)}e.headers.push(a),a.headerGroup=e}),i.push(e),b>0&&k(f,b-1)};k(b.map((a,b)=>j(c,a,{depth:g,index:b})),g-1),i.reverse();let l=a=>a.filter(a=>a.column.getIsVisible()).map(a=>{let b=0,c=0,d=[0];return a.subHeaders&&a.subHeaders.length?(d=[],l(a.subHeaders).forEach(a=>{let{colSpan:c,rowSpan:e}=a;b+=c,d.push(e)})):b=1,c+=Math.min(...d),a.colSpan=b,a.rowSpan=c,{colSpan:b,rowSpan:c}});return l(null!=(e=null==(f=i[0])?void 0:f.headers)?e:[]),i}let l=(a,b,c,d,e,f,i)=>{let j={id:b,index:d,original:c,depth:e,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:b=>{if(j._valuesCache.hasOwnProperty(b))return j._valuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return j._valuesCache[b]=c.accessorFn(j.original,d),j._valuesCache[b]},getUniqueValues:b=>{if(j._uniqueValuesCache.hasOwnProperty(b))return j._uniqueValuesCache[b];let c=a.getColumn(b);if(null!=c&&c.accessorFn)return c.columnDef.getUniqueValues?j._uniqueValuesCache[b]=c.columnDef.getUniqueValues(j.original,d):j._uniqueValuesCache[b]=[j.getValue(b)],j._uniqueValuesCache[b]},renderValue:b=>{var c;return null!=(c=j.getValue(b))?c:a.options.renderFallbackValue},subRows:null!=f?f:[],getLeafRows:()=>(function(a,b){let c=[],d=a=>{a.forEach(a=>{c.push(a);let e=b(a);null!=e&&e.length&&d(e)})};return d(a),c})(j.subRows,a=>a.subRows),getParentRow:()=>j.parentId?a.getRow(j.parentId,!0):void 0,getParentRows:()=>{let a=[],b=j;for(;;){let c=b.getParentRow();if(!c)break;a.push(c),b=c}return a.reverse()},getAllCells:g(()=>[a.getAllLeafColumns()],b=>b.map(b=>(function(a,b,c,d){let e={id:`${b.id}_${c.id}`,row:b,column:c,getValue:()=>b.getValue(d),renderValue:()=>{var b;return null!=(b=e.getValue())?b:a.options.renderFallbackValue},getContext:g(()=>[a,c,b,e],(a,b,c,d)=>({table:a,column:b,row:c,cell:d,getValue:d.getValue,renderValue:d.renderValue}),h(a.options,"debugCells","cell.getContext"))};return a._features.forEach(d=>{null==d.createCell||d.createCell(e,c,b,a)},{}),e})(a,j,b,b.id)),h(a.options,"debugRows","getAllCells")),_getAllCellsByColumnId:g(()=>[j.getAllCells()],a=>a.reduce((a,b)=>(a[b.column.id]=b,a),{}),h(a.options,"debugRows","getAllCellsByColumnId"))};for(let b=0;b<a._features.length;b++){let c=a._features[b];null==c||null==c.createRow||c.createRow(j,a)}return j},m=(a,b,c)=>{var d,e;let f=null==c||null==(d=c.toString())?void 0:d.toLowerCase();return!!(null==(e=a.getValue(b))||null==(e=e.toString())||null==(e=e.toLowerCase())?void 0:e.includes(f))};m.autoRemove=a=>w(a);let n=(a,b,c)=>{var d;return!!(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.includes(c))};n.autoRemove=a=>w(a);let o=(a,b,c)=>{var d;return(null==(d=a.getValue(b))||null==(d=d.toString())?void 0:d.toLowerCase())===(null==c?void 0:c.toLowerCase())};o.autoRemove=a=>w(a);let p=(a,b,c)=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)};p.autoRemove=a=>w(a);let q=(a,b,c)=>!c.some(c=>{var d;return!(null!=(d=a.getValue(b))&&d.includes(c))});q.autoRemove=a=>w(a)||!(null!=a&&a.length);let r=(a,b,c)=>c.some(c=>{var d;return null==(d=a.getValue(b))?void 0:d.includes(c)});r.autoRemove=a=>w(a)||!(null!=a&&a.length);let s=(a,b,c)=>a.getValue(b)===c;s.autoRemove=a=>w(a);let t=(a,b,c)=>a.getValue(b)==c;t.autoRemove=a=>w(a);let u=(a,b,c)=>{let[d,e]=c,f=a.getValue(b);return f>=d&&f<=e};u.resolveFilterValue=a=>{let[b,c]=a,d="number"!=typeof b?parseFloat(b):b,e="number"!=typeof c?parseFloat(c):c,f=null===b||Number.isNaN(d)?-1/0:d,g=null===c||Number.isNaN(e)?1/0:e;if(f>g){let a=f;f=g,g=a}return[f,g]},u.autoRemove=a=>w(a)||w(a[0])&&w(a[1]);let v={includesString:m,includesStringSensitive:n,equalsString:o,arrIncludes:p,arrIncludesAll:q,arrIncludesSome:r,equals:s,weakEquals:t,inNumberRange:u};function w(a){return null==a||""===a}function x(a,b,c){return!!a&&!!a.autoRemove&&a.autoRemove(b,c)||void 0===b||"string"==typeof b&&!b}let y={sum:(a,b,c)=>c.reduce((b,c)=>{let d=c.getValue(a);return b+("number"==typeof d?d:0)},0),min:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d>c||void 0===d&&c>=c)&&(d=c)}),d},max:(a,b,c)=>{let d;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(d<c||void 0===d&&c>=c)&&(d=c)}),d},extent:(a,b,c)=>{let d,e;return c.forEach(b=>{let c=b.getValue(a);null!=c&&(void 0===d?c>=c&&(d=e=c):(d>c&&(d=c),e<c&&(e=c)))}),[d,e]},mean:(a,b)=>{let c=0,d=0;if(b.forEach(b=>{let e=b.getValue(a);null!=e&&(e*=1)>=e&&(++c,d+=e)}),c)return d/c},median:(a,b)=>{if(!b.length)return;let c=b.map(b=>b.getValue(a));if(!function(a){return Array.isArray(a)&&a.every(a=>"number"==typeof a)}(c))return;if(1===c.length)return c[0];let d=Math.floor(c.length/2),e=c.sort((a,b)=>a-b);return c.length%2!=0?e[d]:(e[d-1]+e[d])/2},unique:(a,b)=>Array.from(new Set(b.map(b=>b.getValue(a))).values()),uniqueCount:(a,b)=>new Set(b.map(b=>b.getValue(a))).size,count:(a,b)=>b.length},z=()=>({left:[],right:[]}),A={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},B=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),C=null;function D(a){return"touchstart"===a.type}function E(a,b){return b?"center"===b?a.getCenterVisibleLeafColumns():"left"===b?a.getLeftVisibleLeafColumns():a.getRightVisibleLeafColumns():a.getVisibleLeafColumns()}let F=()=>({pageIndex:0,pageSize:10}),G=()=>({top:[],bottom:[]}),H=(a,b,c,d,e)=>{var f;let g=e.getRow(b,!0);c?(g.getCanMultiSelect()||Object.keys(a).forEach(b=>delete a[b]),g.getCanSelect()&&(a[b]=!0)):delete a[b],d&&null!=(f=g.subRows)&&f.length&&g.getCanSelectSubRows()&&g.subRows.forEach(b=>H(a,b.id,c,d,e))};function I(a,b){let c=a.getState().rowSelection,d=[],e={},f=function(a,b){return a.map(a=>{var b;let g=J(a,c);if(g&&(d.push(a),e[a.id]=a),null!=(b=a.subRows)&&b.length&&(a={...a,subRows:f(a.subRows)}),g)return a}).filter(Boolean)};return{rows:f(b.rows),flatRows:d,rowsById:e}}function J(a,b){var c;return null!=(c=b[a.id])&&c}function K(a,b,c){var d;if(!(null!=(d=a.subRows)&&d.length))return!1;let e=!0,f=!1;return a.subRows.forEach(a=>{if((!f||e)&&(a.getCanSelect()&&(J(a,b)?f=!0:e=!1),a.subRows&&a.subRows.length)){let c=K(a,b);"all"===c?f=!0:("some"===c&&(f=!0),e=!1)}}),e?"all":!!f&&"some"}let L=/([0-9]+)/gm;function M(a,b){return a===b?0:a>b?1:-1}function N(a){return"number"==typeof a?isNaN(a)||a===1/0||a===-1/0?"":String(a):"string"==typeof a?a:""}function O(a,b){let c=a.split(L).filter(Boolean),d=b.split(L).filter(Boolean);for(;c.length&&d.length;){let a=c.shift(),b=d.shift(),e=parseInt(a,10),f=parseInt(b,10),g=[e,f].sort();if(isNaN(g[0])){if(a>b)return 1;if(b>a)return -1;continue}if(isNaN(g[1]))return isNaN(e)?-1:1;if(e>f)return 1;if(f>e)return -1}return c.length-d.length}let P={alphanumeric:(a,b,c)=>O(N(a.getValue(c)).toLowerCase(),N(b.getValue(c)).toLowerCase()),alphanumericCaseSensitive:(a,b,c)=>O(N(a.getValue(c)),N(b.getValue(c))),text:(a,b,c)=>M(N(a.getValue(c)).toLowerCase(),N(b.getValue(c)).toLowerCase()),textCaseSensitive:(a,b,c)=>M(N(a.getValue(c)),N(b.getValue(c))),datetime:(a,b,c)=>{let d=a.getValue(c),e=b.getValue(c);return d>e?1:d<e?-1:0},basic:(a,b,c)=>M(a.getValue(c),b.getValue(c))},Q=[{createTable:a=>{a.getHeaderGroups=g(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>{var f,g;let h=null!=(f=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?f:[],i=null!=(g=null==e?void 0:e.map(a=>c.find(b=>b.id===a)).filter(Boolean))?g:[];return k(b,[...h,...c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),...i],a)},h(a.options,i,"getHeaderGroups")),a.getCenterHeaderGroups=g(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(b,c,d,e)=>k(b,c=c.filter(a=>!(null!=d&&d.includes(a.id))&&!(null!=e&&e.includes(a.id))),a,"center"),h(a.options,i,"getCenterHeaderGroups")),a.getLeftHeaderGroups=g(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.left],(b,c,d)=>{var e;return k(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"left")},h(a.options,i,"getLeftHeaderGroups")),a.getRightHeaderGroups=g(()=>[a.getAllColumns(),a.getVisibleLeafColumns(),a.getState().columnPinning.right],(b,c,d)=>{var e;return k(b,null!=(e=null==d?void 0:d.map(a=>c.find(b=>b.id===a)).filter(Boolean))?e:[],a,"right")},h(a.options,i,"getRightHeaderGroups")),a.getFooterGroups=g(()=>[a.getHeaderGroups()],a=>[...a].reverse(),h(a.options,i,"getFooterGroups")),a.getLeftFooterGroups=g(()=>[a.getLeftHeaderGroups()],a=>[...a].reverse(),h(a.options,i,"getLeftFooterGroups")),a.getCenterFooterGroups=g(()=>[a.getCenterHeaderGroups()],a=>[...a].reverse(),h(a.options,i,"getCenterFooterGroups")),a.getRightFooterGroups=g(()=>[a.getRightHeaderGroups()],a=>[...a].reverse(),h(a.options,i,"getRightFooterGroups")),a.getFlatHeaders=g(()=>[a.getHeaderGroups()],a=>a.map(a=>a.headers).flat(),h(a.options,i,"getFlatHeaders")),a.getLeftFlatHeaders=g(()=>[a.getLeftHeaderGroups()],a=>a.map(a=>a.headers).flat(),h(a.options,i,"getLeftFlatHeaders")),a.getCenterFlatHeaders=g(()=>[a.getCenterHeaderGroups()],a=>a.map(a=>a.headers).flat(),h(a.options,i,"getCenterFlatHeaders")),a.getRightFlatHeaders=g(()=>[a.getRightHeaderGroups()],a=>a.map(a=>a.headers).flat(),h(a.options,i,"getRightFlatHeaders")),a.getCenterLeafHeaders=g(()=>[a.getCenterFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),h(a.options,i,"getCenterLeafHeaders")),a.getLeftLeafHeaders=g(()=>[a.getLeftFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),h(a.options,i,"getLeftLeafHeaders")),a.getRightLeafHeaders=g(()=>[a.getRightFlatHeaders()],a=>a.filter(a=>{var b;return!(null!=(b=a.subHeaders)&&b.length)}),h(a.options,i,"getRightLeafHeaders")),a.getLeafHeaders=g(()=>[a.getLeftHeaderGroups(),a.getCenterHeaderGroups(),a.getRightHeaderGroups()],(a,b,c)=>{var d,e,f,g,h,i;return[...null!=(d=null==(e=a[0])?void 0:e.headers)?d:[],...null!=(f=null==(g=b[0])?void 0:g.headers)?f:[],...null!=(h=null==(i=c[0])?void 0:i.headers)?h:[]].map(a=>a.getLeafHeaders()).flat()},h(a.options,i,"getLeafHeaders"))}},{getInitialState:a=>({columnVisibility:{},...a}),getDefaultOptions:a=>({onColumnVisibilityChange:e("columnVisibility",a)}),createColumn:(a,b)=>{a.toggleVisibility=c=>{a.getCanHide()&&b.setColumnVisibility(b=>({...b,[a.id]:null!=c?c:!a.getIsVisible()}))},a.getIsVisible=()=>{var c,d;let e=a.columns;return null==(c=e.length?e.some(a=>a.getIsVisible()):null==(d=b.getState().columnVisibility)?void 0:d[a.id])||c},a.getCanHide=()=>{var c,d;return(null==(c=a.columnDef.enableHiding)||c)&&(null==(d=b.options.enableHiding)||d)},a.getToggleVisibilityHandler=()=>b=>{null==a.toggleVisibility||a.toggleVisibility(b.target.checked)}},createRow:(a,b)=>{a._getAllVisibleCells=g(()=>[a.getAllCells(),b.getState().columnVisibility],a=>a.filter(a=>a.column.getIsVisible()),h(b.options,"debugRows","_getAllVisibleCells")),a.getVisibleCells=g(()=>[a.getLeftVisibleCells(),a.getCenterVisibleCells(),a.getRightVisibleCells()],(a,b,c)=>[...a,...b,...c],h(b.options,"debugRows","getVisibleCells"))},createTable:a=>{let b=(b,c)=>g(()=>[c(),c().filter(a=>a.getIsVisible()).map(a=>a.id).join("_")],a=>a.filter(a=>null==a.getIsVisible?void 0:a.getIsVisible()),h(a.options,"debugColumns",b));a.getVisibleFlatColumns=b("getVisibleFlatColumns",()=>a.getAllFlatColumns()),a.getVisibleLeafColumns=b("getVisibleLeafColumns",()=>a.getAllLeafColumns()),a.getLeftVisibleLeafColumns=b("getLeftVisibleLeafColumns",()=>a.getLeftLeafColumns()),a.getRightVisibleLeafColumns=b("getRightVisibleLeafColumns",()=>a.getRightLeafColumns()),a.getCenterVisibleLeafColumns=b("getCenterVisibleLeafColumns",()=>a.getCenterLeafColumns()),a.setColumnVisibility=b=>null==a.options.onColumnVisibilityChange?void 0:a.options.onColumnVisibilityChange(b),a.resetColumnVisibility=b=>{var c;a.setColumnVisibility(b?{}:null!=(c=a.initialState.columnVisibility)?c:{})},a.toggleAllColumnsVisible=b=>{var c;b=null!=(c=b)?c:!a.getIsAllColumnsVisible(),a.setColumnVisibility(a.getAllLeafColumns().reduce((a,c)=>({...a,[c.id]:b||!(null!=c.getCanHide&&c.getCanHide())}),{}))},a.getIsAllColumnsVisible=()=>!a.getAllLeafColumns().some(a=>!(null!=a.getIsVisible&&a.getIsVisible())),a.getIsSomeColumnsVisible=()=>a.getAllLeafColumns().some(a=>null==a.getIsVisible?void 0:a.getIsVisible()),a.getToggleAllColumnsVisibilityHandler=()=>b=>{var c;a.toggleAllColumnsVisible(null==(c=b.target)?void 0:c.checked)}}},{getInitialState:a=>({columnOrder:[],...a}),getDefaultOptions:a=>({onColumnOrderChange:e("columnOrder",a)}),createColumn:(a,b)=>{a.getIndex=g(a=>[E(b,a)],b=>b.findIndex(b=>b.id===a.id),h(b.options,"debugColumns","getIndex")),a.getIsFirstColumn=c=>{var d;return(null==(d=E(b,c)[0])?void 0:d.id)===a.id},a.getIsLastColumn=c=>{var d;let e=E(b,c);return(null==(d=e[e.length-1])?void 0:d.id)===a.id}},createTable:a=>{a.setColumnOrder=b=>null==a.options.onColumnOrderChange?void 0:a.options.onColumnOrderChange(b),a.resetColumnOrder=b=>{var c;a.setColumnOrder(b?[]:null!=(c=a.initialState.columnOrder)?c:[])},a._getOrderColumnsFn=g(()=>[a.getState().columnOrder,a.getState().grouping,a.options.groupedColumnMode],(a,b,c)=>d=>{let e=[];if(null!=a&&a.length){let b=[...a],c=[...d];for(;c.length&&b.length;){let a=b.shift(),d=c.findIndex(b=>b.id===a);d>-1&&e.push(c.splice(d,1)[0])}e=[...e,...c]}else e=d;return function(a,b,c){if(!(null!=b&&b.length)||!c)return a;let d=a.filter(a=>!b.includes(a.id));return"remove"===c?d:[...b.map(b=>a.find(a=>a.id===b)).filter(Boolean),...d]}(e,b,c)},h(a.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:a=>({columnPinning:z(),...a}),getDefaultOptions:a=>({onColumnPinningChange:e("columnPinning",a)}),createColumn:(a,b)=>{a.pin=c=>{let d=a.getLeafColumns().map(a=>a.id).filter(Boolean);b.setColumnPinning(a=>{var b,e,f,g,h,i;return"right"===c?{left:(null!=(f=null==a?void 0:a.left)?f:[]).filter(a=>!(null!=d&&d.includes(a))),right:[...(null!=(g=null==a?void 0:a.right)?g:[]).filter(a=>!(null!=d&&d.includes(a))),...d]}:"left"===c?{left:[...(null!=(h=null==a?void 0:a.left)?h:[]).filter(a=>!(null!=d&&d.includes(a))),...d],right:(null!=(i=null==a?void 0:a.right)?i:[]).filter(a=>!(null!=d&&d.includes(a)))}:{left:(null!=(b=null==a?void 0:a.left)?b:[]).filter(a=>!(null!=d&&d.includes(a))),right:(null!=(e=null==a?void 0:a.right)?e:[]).filter(a=>!(null!=d&&d.includes(a)))}})},a.getCanPin=()=>a.getLeafColumns().some(a=>{var c,d,e;return(null==(c=a.columnDef.enablePinning)||c)&&(null==(d=null!=(e=b.options.enableColumnPinning)?e:b.options.enablePinning)||d)}),a.getIsPinned=()=>{let c=a.getLeafColumns().map(a=>a.id),{left:d,right:e}=b.getState().columnPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"left":!!g&&"right"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();return e?null!=(c=null==(d=b.getState().columnPinning)||null==(d=d[e])?void 0:d.indexOf(a.id))?c:-1:0}},createRow:(a,b)=>{a.getCenterVisibleCells=g(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left,b.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.column.id))},h(b.options,"debugRows","getCenterVisibleCells")),a.getLeftVisibleCells=g(()=>[a._getAllVisibleCells(),b.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"left"})),h(b.options,"debugRows","getLeftVisibleCells")),a.getRightVisibleCells=g(()=>[a._getAllVisibleCells(),b.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.column.id===b)).filter(Boolean).map(a=>({...a,position:"right"})),h(b.options,"debugRows","getRightVisibleCells"))},createTable:a=>{a.setColumnPinning=b=>null==a.options.onColumnPinningChange?void 0:a.options.onColumnPinningChange(b),a.resetColumnPinning=b=>{var c,d;return a.setColumnPinning(b?z():null!=(c=null==(d=a.initialState)?void 0:d.columnPinning)?c:z())},a.getIsSomeColumnsPinned=b=>{var c,d,e;let f=a.getState().columnPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.left)?void 0:d.length)||(null==(e=f.right)?void 0:e.length))},a.getLeftLeafColumns=g(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),h(a.options,"debugColumns","getLeftLeafColumns")),a.getRightLeafColumns=g(()=>[a.getAllLeafColumns(),a.getState().columnPinning.right],(a,b)=>(null!=b?b:[]).map(b=>a.find(a=>a.id===b)).filter(Boolean),h(a.options,"debugColumns","getRightLeafColumns")),a.getCenterLeafColumns=g(()=>[a.getAllLeafColumns(),a.getState().columnPinning.left,a.getState().columnPinning.right],(a,b,c)=>{let d=[...null!=b?b:[],...null!=c?c:[]];return a.filter(a=>!d.includes(a.id))},h(a.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(a,b)=>{a._getFacetedRowModel=b.options.getFacetedRowModel&&b.options.getFacetedRowModel(b,a.id),a.getFacetedRowModel=()=>a._getFacetedRowModel?a._getFacetedRowModel():b.getPreFilteredRowModel(),a._getFacetedUniqueValues=b.options.getFacetedUniqueValues&&b.options.getFacetedUniqueValues(b,a.id),a.getFacetedUniqueValues=()=>a._getFacetedUniqueValues?a._getFacetedUniqueValues():new Map,a._getFacetedMinMaxValues=b.options.getFacetedMinMaxValues&&b.options.getFacetedMinMaxValues(b,a.id),a.getFacetedMinMaxValues=()=>{if(a._getFacetedMinMaxValues)return a._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:a=>({columnFilters:[],...a}),getDefaultOptions:a=>({onColumnFiltersChange:e("columnFilters",a),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(a,b)=>{a.getAutoFilterFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"string"==typeof d?v.includesString:"number"==typeof d?v.inNumberRange:"boolean"==typeof d||null!==d&&"object"==typeof d?v.equals:Array.isArray(d)?v.arrIncludes:v.weakEquals},a.getFilterFn=()=>{var c,d;return f(a.columnDef.filterFn)?a.columnDef.filterFn:"auto"===a.columnDef.filterFn?a.getAutoFilterFn():null!=(c=null==(d=b.options.filterFns)?void 0:d[a.columnDef.filterFn])?c:v[a.columnDef.filterFn]},a.getCanFilter=()=>{var c,d,e;return(null==(c=a.columnDef.enableColumnFilter)||c)&&(null==(d=b.options.enableColumnFilters)||d)&&(null==(e=b.options.enableFilters)||e)&&!!a.accessorFn},a.getIsFiltered=()=>a.getFilterIndex()>-1,a.getFilterValue=()=>{var c;return null==(c=b.getState().columnFilters)||null==(c=c.find(b=>b.id===a.id))?void 0:c.value},a.getFilterIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().columnFilters)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.setFilterValue=c=>{b.setColumnFilters(b=>{var e,f;let g=a.getFilterFn(),h=null==b?void 0:b.find(b=>b.id===a.id),i=d(c,h?h.value:void 0);if(x(g,i,a))return null!=(e=null==b?void 0:b.filter(b=>b.id!==a.id))?e:[];let j={id:a.id,value:i};return h?null!=(f=null==b?void 0:b.map(b=>b.id===a.id?j:b))?f:[]:null!=b&&b.length?[...b,j]:[j]})}},createRow:(a,b)=>{a.columnFilters={},a.columnFiltersMeta={}},createTable:a=>{a.setColumnFilters=b=>{let c=a.getAllLeafColumns();null==a.options.onColumnFiltersChange||a.options.onColumnFiltersChange(a=>{var e;return null==(e=d(b,a))?void 0:e.filter(a=>{let b=c.find(b=>b.id===a.id);return!(b&&x(b.getFilterFn(),a.value,b))&&!0})})},a.resetColumnFilters=b=>{var c,d;a.setColumnFilters(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.columnFilters)?c:[])},a.getPreFilteredRowModel=()=>a.getCoreRowModel(),a.getFilteredRowModel=()=>(!a._getFilteredRowModel&&a.options.getFilteredRowModel&&(a._getFilteredRowModel=a.options.getFilteredRowModel(a)),a.options.manualFiltering||!a._getFilteredRowModel)?a.getPreFilteredRowModel():a._getFilteredRowModel()}},{createTable:a=>{a._getGlobalFacetedRowModel=a.options.getFacetedRowModel&&a.options.getFacetedRowModel(a,"__global__"),a.getGlobalFacetedRowModel=()=>a.options.manualFiltering||!a._getGlobalFacetedRowModel?a.getPreFilteredRowModel():a._getGlobalFacetedRowModel(),a._getGlobalFacetedUniqueValues=a.options.getFacetedUniqueValues&&a.options.getFacetedUniqueValues(a,"__global__"),a.getGlobalFacetedUniqueValues=()=>a._getGlobalFacetedUniqueValues?a._getGlobalFacetedUniqueValues():new Map,a._getGlobalFacetedMinMaxValues=a.options.getFacetedMinMaxValues&&a.options.getFacetedMinMaxValues(a,"__global__"),a.getGlobalFacetedMinMaxValues=()=>{if(a._getGlobalFacetedMinMaxValues)return a._getGlobalFacetedMinMaxValues()}}},{getInitialState:a=>({globalFilter:void 0,...a}),getDefaultOptions:a=>({onGlobalFilterChange:e("globalFilter",a),globalFilterFn:"auto",getColumnCanGlobalFilter:b=>{var c;let d=null==(c=a.getCoreRowModel().flatRows[0])||null==(c=c._getAllCellsByColumnId()[b.id])?void 0:c.getValue();return"string"==typeof d||"number"==typeof d}}),createColumn:(a,b)=>{a.getCanGlobalFilter=()=>{var c,d,e,f;return(null==(c=a.columnDef.enableGlobalFilter)||c)&&(null==(d=b.options.enableGlobalFilter)||d)&&(null==(e=b.options.enableFilters)||e)&&(null==(f=null==b.options.getColumnCanGlobalFilter?void 0:b.options.getColumnCanGlobalFilter(a))||f)&&!!a.accessorFn}},createTable:a=>{a.getGlobalAutoFilterFn=()=>v.includesString,a.getGlobalFilterFn=()=>{var b,c;let{globalFilterFn:d}=a.options;return f(d)?d:"auto"===d?a.getGlobalAutoFilterFn():null!=(b=null==(c=a.options.filterFns)?void 0:c[d])?b:v[d]},a.setGlobalFilter=b=>{null==a.options.onGlobalFilterChange||a.options.onGlobalFilterChange(b)},a.resetGlobalFilter=b=>{a.setGlobalFilter(b?void 0:a.initialState.globalFilter)}}},{getInitialState:a=>({sorting:[],...a}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:a=>({onSortingChange:e("sorting",a),isMultiSortEvent:a=>a.shiftKey}),createColumn:(a,b)=>{a.getAutoSortingFn=()=>{let c=b.getFilteredRowModel().flatRows.slice(10),d=!1;for(let b of c){let c=null==b?void 0:b.getValue(a.id);if("[object Date]"===Object.prototype.toString.call(c))return P.datetime;if("string"==typeof c&&(d=!0,c.split(L).length>1))return P.alphanumeric}return d?P.text:P.basic},a.getAutoSortDir=()=>{let c=b.getFilteredRowModel().flatRows[0];return"string"==typeof(null==c?void 0:c.getValue(a.id))?"asc":"desc"},a.getSortingFn=()=>{var c,d;if(!a)throw Error();return f(a.columnDef.sortingFn)?a.columnDef.sortingFn:"auto"===a.columnDef.sortingFn?a.getAutoSortingFn():null!=(c=null==(d=b.options.sortingFns)?void 0:d[a.columnDef.sortingFn])?c:P[a.columnDef.sortingFn]},a.toggleSorting=(c,d)=>{let e=a.getNextSortingOrder(),f=null!=c;b.setSorting(g=>{let h,i=null==g?void 0:g.find(b=>b.id===a.id),j=null==g?void 0:g.findIndex(b=>b.id===a.id),k=[],l=f?c:"desc"===e;if("toggle"!=(h=null!=g&&g.length&&a.getCanMultiSort()&&d?i?"toggle":"add":null!=g&&g.length&&j!==g.length-1?"replace":i?"toggle":"replace")||f||e||(h="remove"),"add"===h){var m;(k=[...g,{id:a.id,desc:l}]).splice(0,k.length-(null!=(m=b.options.maxMultiSortColCount)?m:Number.MAX_SAFE_INTEGER))}else k="toggle"===h?g.map(b=>b.id===a.id?{...b,desc:l}:b):"remove"===h?g.filter(b=>b.id!==a.id):[{id:a.id,desc:l}];return k})},a.getFirstSortDir=()=>{var c,d;return(null!=(c=null!=(d=a.columnDef.sortDescFirst)?d:b.options.sortDescFirst)?c:"desc"===a.getAutoSortDir())?"desc":"asc"},a.getNextSortingOrder=c=>{var d,e;let f=a.getFirstSortDir(),g=a.getIsSorted();return g?(g===f||null!=(d=b.options.enableSortingRemoval)&&!d||!!c&&null!=(e=b.options.enableMultiRemove)&&!e)&&("desc"===g?"asc":"desc"):f},a.getCanSort=()=>{var c,d;return(null==(c=a.columnDef.enableSorting)||c)&&(null==(d=b.options.enableSorting)||d)&&!!a.accessorFn},a.getCanMultiSort=()=>{var c,d;return null!=(c=null!=(d=a.columnDef.enableMultiSort)?d:b.options.enableMultiSort)?c:!!a.accessorFn},a.getIsSorted=()=>{var c;let d=null==(c=b.getState().sorting)?void 0:c.find(b=>b.id===a.id);return!!d&&(d.desc?"desc":"asc")},a.getSortIndex=()=>{var c,d;return null!=(c=null==(d=b.getState().sorting)?void 0:d.findIndex(b=>b.id===a.id))?c:-1},a.clearSorting=()=>{b.setSorting(b=>null!=b&&b.length?b.filter(b=>b.id!==a.id):[])},a.getToggleSortingHandler=()=>{let c=a.getCanSort();return d=>{c&&(null==d.persist||d.persist(),null==a.toggleSorting||a.toggleSorting(void 0,!!a.getCanMultiSort()&&(null==b.options.isMultiSortEvent?void 0:b.options.isMultiSortEvent(d))))}}},createTable:a=>{a.setSorting=b=>null==a.options.onSortingChange?void 0:a.options.onSortingChange(b),a.resetSorting=b=>{var c,d;a.setSorting(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.sorting)?c:[])},a.getPreSortedRowModel=()=>a.getGroupedRowModel(),a.getSortedRowModel=()=>(!a._getSortedRowModel&&a.options.getSortedRowModel&&(a._getSortedRowModel=a.options.getSortedRowModel(a)),a.options.manualSorting||!a._getSortedRowModel)?a.getPreSortedRowModel():a._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:a=>{var b,c;return null!=(b=null==(c=a.getValue())||null==c.toString?void 0:c.toString())?b:null},aggregationFn:"auto"}),getInitialState:a=>({grouping:[],...a}),getDefaultOptions:a=>({onGroupingChange:e("grouping",a),groupedColumnMode:"reorder"}),createColumn:(a,b)=>{a.toggleGrouping=()=>{b.setGrouping(b=>null!=b&&b.includes(a.id)?b.filter(b=>b!==a.id):[...null!=b?b:[],a.id])},a.getCanGroup=()=>{var c,d;return(null==(c=a.columnDef.enableGrouping)||c)&&(null==(d=b.options.enableGrouping)||d)&&(!!a.accessorFn||!!a.columnDef.getGroupingValue)},a.getIsGrouped=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.includes(a.id)},a.getGroupedIndex=()=>{var c;return null==(c=b.getState().grouping)?void 0:c.indexOf(a.id)},a.getToggleGroupingHandler=()=>{let b=a.getCanGroup();return()=>{b&&a.toggleGrouping()}},a.getAutoAggregationFn=()=>{let c=b.getCoreRowModel().flatRows[0],d=null==c?void 0:c.getValue(a.id);return"number"==typeof d?y.sum:"[object Date]"===Object.prototype.toString.call(d)?y.extent:void 0},a.getAggregationFn=()=>{var c,d;if(!a)throw Error();return f(a.columnDef.aggregationFn)?a.columnDef.aggregationFn:"auto"===a.columnDef.aggregationFn?a.getAutoAggregationFn():null!=(c=null==(d=b.options.aggregationFns)?void 0:d[a.columnDef.aggregationFn])?c:y[a.columnDef.aggregationFn]}},createTable:a=>{a.setGrouping=b=>null==a.options.onGroupingChange?void 0:a.options.onGroupingChange(b),a.resetGrouping=b=>{var c,d;a.setGrouping(b?[]:null!=(c=null==(d=a.initialState)?void 0:d.grouping)?c:[])},a.getPreGroupedRowModel=()=>a.getFilteredRowModel(),a.getGroupedRowModel=()=>(!a._getGroupedRowModel&&a.options.getGroupedRowModel&&(a._getGroupedRowModel=a.options.getGroupedRowModel(a)),a.options.manualGrouping||!a._getGroupedRowModel)?a.getPreGroupedRowModel():a._getGroupedRowModel()},createRow:(a,b)=>{a.getIsGrouped=()=>!!a.groupingColumnId,a.getGroupingValue=c=>{if(a._groupingValuesCache.hasOwnProperty(c))return a._groupingValuesCache[c];let d=b.getColumn(c);return null!=d&&d.columnDef.getGroupingValue?(a._groupingValuesCache[c]=d.columnDef.getGroupingValue(a.original),a._groupingValuesCache[c]):a.getValue(c)},a._groupingValuesCache={}},createCell:(a,b,c,d)=>{a.getIsGrouped=()=>b.getIsGrouped()&&b.id===c.groupingColumnId,a.getIsPlaceholder=()=>!a.getIsGrouped()&&b.getIsGrouped(),a.getIsAggregated=()=>{var b;return!a.getIsGrouped()&&!a.getIsPlaceholder()&&!!(null!=(b=c.subRows)&&b.length)}}},{getInitialState:a=>({expanded:{},...a}),getDefaultOptions:a=>({onExpandedChange:e("expanded",a),paginateExpandedRows:!0}),createTable:a=>{let b=!1,c=!1;a._autoResetExpanded=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetExpanded)?d:!a.options.manualExpanding){if(c)return;c=!0,a._queue(()=>{a.resetExpanded(),c=!1})}},a.setExpanded=b=>null==a.options.onExpandedChange?void 0:a.options.onExpandedChange(b),a.toggleAllRowsExpanded=b=>{(null!=b?b:!a.getIsAllRowsExpanded())?a.setExpanded(!0):a.setExpanded({})},a.resetExpanded=b=>{var c,d;a.setExpanded(b?{}:null!=(c=null==(d=a.initialState)?void 0:d.expanded)?c:{})},a.getCanSomeRowsExpand=()=>a.getPrePaginationRowModel().flatRows.some(a=>a.getCanExpand()),a.getToggleAllRowsExpandedHandler=()=>b=>{null==b.persist||b.persist(),a.toggleAllRowsExpanded()},a.getIsSomeRowsExpanded=()=>{let b=a.getState().expanded;return!0===b||Object.values(b).some(Boolean)},a.getIsAllRowsExpanded=()=>{let b=a.getState().expanded;return"boolean"==typeof b?!0===b:!(!Object.keys(b).length||a.getRowModel().flatRows.some(a=>!a.getIsExpanded()))},a.getExpandedDepth=()=>{let b=0;return(!0===a.getState().expanded?Object.keys(a.getRowModel().rowsById):Object.keys(a.getState().expanded)).forEach(a=>{let c=a.split(".");b=Math.max(b,c.length)}),b},a.getPreExpandedRowModel=()=>a.getSortedRowModel(),a.getExpandedRowModel=()=>(!a._getExpandedRowModel&&a.options.getExpandedRowModel&&(a._getExpandedRowModel=a.options.getExpandedRowModel(a)),a.options.manualExpanding||!a._getExpandedRowModel)?a.getPreExpandedRowModel():a._getExpandedRowModel()},createRow:(a,b)=>{a.toggleExpanded=c=>{b.setExpanded(d=>{var e;let f=!0===d||!!(null!=d&&d[a.id]),g={};if(!0===d?Object.keys(b.getRowModel().rowsById).forEach(a=>{g[a]=!0}):g=d,c=null!=(e=c)?e:!f,!f&&c)return{...g,[a.id]:!0};if(f&&!c){let{[a.id]:b,...c}=g;return c}return d})},a.getIsExpanded=()=>{var c;let d=b.getState().expanded;return!!(null!=(c=null==b.options.getIsRowExpanded?void 0:b.options.getIsRowExpanded(a))?c:!0===d||(null==d?void 0:d[a.id]))},a.getCanExpand=()=>{var c,d,e;return null!=(c=null==b.options.getRowCanExpand?void 0:b.options.getRowCanExpand(a))?c:(null==(d=b.options.enableExpanding)||d)&&!!(null!=(e=a.subRows)&&e.length)},a.getIsAllParentsExpanded=()=>{let c=!0,d=a;for(;c&&d.parentId;)c=(d=b.getRow(d.parentId,!0)).getIsExpanded();return c},a.getToggleExpandedHandler=()=>{let b=a.getCanExpand();return()=>{b&&a.toggleExpanded()}}}},{getInitialState:a=>({...a,pagination:{...F(),...null==a?void 0:a.pagination}}),getDefaultOptions:a=>({onPaginationChange:e("pagination",a)}),createTable:a=>{let b=!1,c=!1;a._autoResetPageIndex=()=>{var d,e;if(!b)return void a._queue(()=>{b=!0});if(null!=(d=null!=(e=a.options.autoResetAll)?e:a.options.autoResetPageIndex)?d:!a.options.manualPagination){if(c)return;c=!0,a._queue(()=>{a.resetPageIndex(),c=!1})}},a.setPagination=b=>null==a.options.onPaginationChange?void 0:a.options.onPaginationChange(a=>d(b,a)),a.resetPagination=b=>{var c;a.setPagination(b?F():null!=(c=a.initialState.pagination)?c:F())},a.setPageIndex=b=>{a.setPagination(c=>{let e=d(b,c.pageIndex);return e=Math.max(0,Math.min(e,void 0===a.options.pageCount||-1===a.options.pageCount?Number.MAX_SAFE_INTEGER:a.options.pageCount-1)),{...c,pageIndex:e}})},a.resetPageIndex=b=>{var c,d;a.setPageIndex(b?0:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageIndex)?c:0)},a.resetPageSize=b=>{var c,d;a.setPageSize(b?10:null!=(c=null==(d=a.initialState)||null==(d=d.pagination)?void 0:d.pageSize)?c:10)},a.setPageSize=b=>{a.setPagination(a=>{let c=Math.max(1,d(b,a.pageSize)),e=Math.floor(a.pageSize*a.pageIndex/c);return{...a,pageIndex:e,pageSize:c}})},a.setPageCount=b=>a.setPagination(c=>{var e;let f=d(b,null!=(e=a.options.pageCount)?e:-1);return"number"==typeof f&&(f=Math.max(-1,f)),{...c,pageCount:f}}),a.getPageOptions=g(()=>[a.getPageCount()],a=>{let b=[];return a&&a>0&&(b=[...Array(a)].fill(null).map((a,b)=>b)),b},h(a.options,"debugTable","getPageOptions")),a.getCanPreviousPage=()=>a.getState().pagination.pageIndex>0,a.getCanNextPage=()=>{let{pageIndex:b}=a.getState().pagination,c=a.getPageCount();return -1===c||0!==c&&b<c-1},a.previousPage=()=>a.setPageIndex(a=>a-1),a.nextPage=()=>a.setPageIndex(a=>a+1),a.firstPage=()=>a.setPageIndex(0),a.lastPage=()=>a.setPageIndex(a.getPageCount()-1),a.getPrePaginationRowModel=()=>a.getExpandedRowModel(),a.getPaginationRowModel=()=>(!a._getPaginationRowModel&&a.options.getPaginationRowModel&&(a._getPaginationRowModel=a.options.getPaginationRowModel(a)),a.options.manualPagination||!a._getPaginationRowModel)?a.getPrePaginationRowModel():a._getPaginationRowModel(),a.getPageCount=()=>{var b;return null!=(b=a.options.pageCount)?b:Math.ceil(a.getRowCount()/a.getState().pagination.pageSize)},a.getRowCount=()=>{var b;return null!=(b=a.options.rowCount)?b:a.getPrePaginationRowModel().rows.length}}},{getInitialState:a=>({rowPinning:G(),...a}),getDefaultOptions:a=>({onRowPinningChange:e("rowPinning",a)}),createRow:(a,b)=>{a.pin=(c,d,e)=>{let f=d?a.getLeafRows().map(a=>{let{id:b}=a;return b}):[],g=new Set([...e?a.getParentRows().map(a=>{let{id:b}=a;return b}):[],a.id,...f]);b.setRowPinning(a=>{var b,d,e,f,h,i;return"bottom"===c?{top:(null!=(e=null==a?void 0:a.top)?e:[]).filter(a=>!(null!=g&&g.has(a))),bottom:[...(null!=(f=null==a?void 0:a.bottom)?f:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)]}:"top"===c?{top:[...(null!=(h=null==a?void 0:a.top)?h:[]).filter(a=>!(null!=g&&g.has(a))),...Array.from(g)],bottom:(null!=(i=null==a?void 0:a.bottom)?i:[]).filter(a=>!(null!=g&&g.has(a)))}:{top:(null!=(b=null==a?void 0:a.top)?b:[]).filter(a=>!(null!=g&&g.has(a))),bottom:(null!=(d=null==a?void 0:a.bottom)?d:[]).filter(a=>!(null!=g&&g.has(a)))}})},a.getCanPin=()=>{var c;let{enableRowPinning:d,enablePinning:e}=b.options;return"function"==typeof d?d(a):null==(c=null!=d?d:e)||c},a.getIsPinned=()=>{let c=[a.id],{top:d,bottom:e}=b.getState().rowPinning,f=c.some(a=>null==d?void 0:d.includes(a)),g=c.some(a=>null==e?void 0:e.includes(a));return f?"top":!!g&&"bottom"},a.getPinnedIndex=()=>{var c,d;let e=a.getIsPinned();if(!e)return -1;let f=null==(c="top"===e?b.getTopRows():b.getBottomRows())?void 0:c.map(a=>{let{id:b}=a;return b});return null!=(d=null==f?void 0:f.indexOf(a.id))?d:-1}},createTable:a=>{a.setRowPinning=b=>null==a.options.onRowPinningChange?void 0:a.options.onRowPinningChange(b),a.resetRowPinning=b=>{var c,d;return a.setRowPinning(b?G():null!=(c=null==(d=a.initialState)?void 0:d.rowPinning)?c:G())},a.getIsSomeRowsPinned=b=>{var c,d,e;let f=a.getState().rowPinning;return b?!!(null==(c=f[b])?void 0:c.length):!!((null==(d=f.top)?void 0:d.length)||(null==(e=f.bottom)?void 0:e.length))},a._getPinnedRows=(b,c,d)=>{var e;return(null==(e=a.options.keepPinnedRows)||e?(null!=c?c:[]).map(b=>{let c=a.getRow(b,!0);return c.getIsAllParentsExpanded()?c:null}):(null!=c?c:[]).map(a=>b.find(b=>b.id===a))).filter(Boolean).map(a=>({...a,position:d}))},a.getTopRows=g(()=>[a.getRowModel().rows,a.getState().rowPinning.top],(b,c)=>a._getPinnedRows(b,c,"top"),h(a.options,"debugRows","getTopRows")),a.getBottomRows=g(()=>[a.getRowModel().rows,a.getState().rowPinning.bottom],(b,c)=>a._getPinnedRows(b,c,"bottom"),h(a.options,"debugRows","getBottomRows")),a.getCenterRows=g(()=>[a.getRowModel().rows,a.getState().rowPinning.top,a.getState().rowPinning.bottom],(a,b,c)=>{let d=new Set([...null!=b?b:[],...null!=c?c:[]]);return a.filter(a=>!d.has(a.id))},h(a.options,"debugRows","getCenterRows"))}},{getInitialState:a=>({rowSelection:{},...a}),getDefaultOptions:a=>({onRowSelectionChange:e("rowSelection",a),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:a=>{a.setRowSelection=b=>null==a.options.onRowSelectionChange?void 0:a.options.onRowSelectionChange(b),a.resetRowSelection=b=>{var c;return a.setRowSelection(b?{}:null!=(c=a.initialState.rowSelection)?c:{})},a.toggleAllRowsSelected=b=>{a.setRowSelection(c=>{b=void 0!==b?b:!a.getIsAllRowsSelected();let d={...c},e=a.getPreGroupedRowModel().flatRows;return b?e.forEach(a=>{a.getCanSelect()&&(d[a.id]=!0)}):e.forEach(a=>{delete d[a.id]}),d})},a.toggleAllPageRowsSelected=b=>a.setRowSelection(c=>{let d=void 0!==b?b:!a.getIsAllPageRowsSelected(),e={...c};return a.getRowModel().rows.forEach(b=>{H(e,b.id,d,!0,a)}),e}),a.getPreSelectedRowModel=()=>a.getCoreRowModel(),a.getSelectedRowModel=g(()=>[a.getState().rowSelection,a.getCoreRowModel()],(b,c)=>Object.keys(b).length?I(a,c):{rows:[],flatRows:[],rowsById:{}},h(a.options,"debugTable","getSelectedRowModel")),a.getFilteredSelectedRowModel=g(()=>[a.getState().rowSelection,a.getFilteredRowModel()],(b,c)=>Object.keys(b).length?I(a,c):{rows:[],flatRows:[],rowsById:{}},h(a.options,"debugTable","getFilteredSelectedRowModel")),a.getGroupedSelectedRowModel=g(()=>[a.getState().rowSelection,a.getSortedRowModel()],(b,c)=>Object.keys(b).length?I(a,c):{rows:[],flatRows:[],rowsById:{}},h(a.options,"debugTable","getGroupedSelectedRowModel")),a.getIsAllRowsSelected=()=>{let b=a.getFilteredRowModel().flatRows,{rowSelection:c}=a.getState(),d=!!(b.length&&Object.keys(c).length);return d&&b.some(a=>a.getCanSelect()&&!c[a.id])&&(d=!1),d},a.getIsAllPageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows.filter(a=>a.getCanSelect()),{rowSelection:c}=a.getState(),d=!!b.length;return d&&b.some(a=>!c[a.id])&&(d=!1),d},a.getIsSomeRowsSelected=()=>{var b;let c=Object.keys(null!=(b=a.getState().rowSelection)?b:{}).length;return c>0&&c<a.getFilteredRowModel().flatRows.length},a.getIsSomePageRowsSelected=()=>{let b=a.getPaginationRowModel().flatRows;return!a.getIsAllPageRowsSelected()&&b.filter(a=>a.getCanSelect()).some(a=>a.getIsSelected()||a.getIsSomeSelected())},a.getToggleAllRowsSelectedHandler=()=>b=>{a.toggleAllRowsSelected(b.target.checked)},a.getToggleAllPageRowsSelectedHandler=()=>b=>{a.toggleAllPageRowsSelected(b.target.checked)}},createRow:(a,b)=>{a.toggleSelected=(c,d)=>{let e=a.getIsSelected();b.setRowSelection(f=>{var g;if(c=void 0!==c?c:!e,a.getCanSelect()&&e===c)return f;let h={...f};return H(h,a.id,c,null==(g=null==d?void 0:d.selectChildren)||g,b),h})},a.getIsSelected=()=>{let{rowSelection:c}=b.getState();return J(a,c)},a.getIsSomeSelected=()=>{let{rowSelection:c}=b.getState();return"some"===K(a,c)},a.getIsAllSubRowsSelected=()=>{let{rowSelection:c}=b.getState();return"all"===K(a,c)},a.getCanSelect=()=>{var c;return"function"==typeof b.options.enableRowSelection?b.options.enableRowSelection(a):null==(c=b.options.enableRowSelection)||c},a.getCanSelectSubRows=()=>{var c;return"function"==typeof b.options.enableSubRowSelection?b.options.enableSubRowSelection(a):null==(c=b.options.enableSubRowSelection)||c},a.getCanMultiSelect=()=>{var c;return"function"==typeof b.options.enableMultiRowSelection?b.options.enableMultiRowSelection(a):null==(c=b.options.enableMultiRowSelection)||c},a.getToggleSelectedHandler=()=>{let b=a.getCanSelect();return c=>{var d;b&&a.toggleSelected(null==(d=c.target)?void 0:d.checked)}}}},{getDefaultColumnDef:()=>A,getInitialState:a=>({columnSizing:{},columnSizingInfo:B(),...a}),getDefaultOptions:a=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:e("columnSizing",a),onColumnSizingInfoChange:e("columnSizingInfo",a)}),createColumn:(a,b)=>{a.getSize=()=>{var c,d,e;let f=b.getState().columnSizing[a.id];return Math.min(Math.max(null!=(c=a.columnDef.minSize)?c:A.minSize,null!=(d=null!=f?f:a.columnDef.size)?d:A.size),null!=(e=a.columnDef.maxSize)?e:A.maxSize)},a.getStart=g(a=>[a,E(b,a),b.getState().columnSizing],(b,c)=>c.slice(0,a.getIndex(b)).reduce((a,b)=>a+b.getSize(),0),h(b.options,"debugColumns","getStart")),a.getAfter=g(a=>[a,E(b,a),b.getState().columnSizing],(b,c)=>c.slice(a.getIndex(b)+1).reduce((a,b)=>a+b.getSize(),0),h(b.options,"debugColumns","getAfter")),a.resetSize=()=>{b.setColumnSizing(b=>{let{[a.id]:c,...d}=b;return d})},a.getCanResize=()=>{var c,d;return(null==(c=a.columnDef.enableResizing)||c)&&(null==(d=b.options.enableColumnResizing)||d)},a.getIsResizing=()=>b.getState().columnSizingInfo.isResizingColumn===a.id},createHeader:(a,b)=>{a.getSize=()=>{let b=0,c=a=>{if(a.subHeaders.length)a.subHeaders.forEach(c);else{var d;b+=null!=(d=a.column.getSize())?d:0}};return c(a),b},a.getStart=()=>{if(a.index>0){let b=a.headerGroup.headers[a.index-1];return b.getStart()+b.getSize()}return 0},a.getResizeHandler=c=>{let d=b.getColumn(a.column.id),e=null==d?void 0:d.getCanResize();return f=>{if(!d||!e||(null==f.persist||f.persist(),D(f)&&f.touches&&f.touches.length>1))return;let g=a.getSize(),h=a?a.getLeafHeaders().map(a=>[a.column.id,a.column.getSize()]):[[d.id,d.getSize()]],i=D(f)?Math.round(f.touches[0].clientX):f.clientX,j={},k=(a,c)=>{"number"==typeof c&&(b.setColumnSizingInfo(a=>{var d,e;let f="rtl"===b.options.columnResizeDirection?-1:1,g=(c-(null!=(d=null==a?void 0:a.startOffset)?d:0))*f,h=Math.max(g/(null!=(e=null==a?void 0:a.startSize)?e:0),-.999999);return a.columnSizingStart.forEach(a=>{let[b,c]=a;j[b]=Math.round(100*Math.max(c+c*h,0))/100}),{...a,deltaOffset:g,deltaPercentage:h}}),("onChange"===b.options.columnResizeMode||"end"===a)&&b.setColumnSizing(a=>({...a,...j})))},l=a=>k("move",a),m=a=>{k("end",a),b.setColumnSizingInfo(a=>({...a,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},n=c||("undefined"!=typeof document?document:null),o={moveHandler:a=>l(a.clientX),upHandler:a=>{null==n||n.removeEventListener("mousemove",o.moveHandler),null==n||n.removeEventListener("mouseup",o.upHandler),m(a.clientX)}},p={moveHandler:a=>(a.cancelable&&(a.preventDefault(),a.stopPropagation()),l(a.touches[0].clientX),!1),upHandler:a=>{var b;null==n||n.removeEventListener("touchmove",p.moveHandler),null==n||n.removeEventListener("touchend",p.upHandler),a.cancelable&&(a.preventDefault(),a.stopPropagation()),m(null==(b=a.touches[0])?void 0:b.clientX)}},q=!!function(){if("boolean"==typeof C)return C;let a=!1;try{let b=()=>{};window.addEventListener("test",b,{get passive(){return a=!0,!1}}),window.removeEventListener("test",b)}catch(b){a=!1}return C=a}()&&{passive:!1};D(f)?(null==n||n.addEventListener("touchmove",p.moveHandler,q),null==n||n.addEventListener("touchend",p.upHandler,q)):(null==n||n.addEventListener("mousemove",o.moveHandler,q),null==n||n.addEventListener("mouseup",o.upHandler,q)),b.setColumnSizingInfo(a=>({...a,startOffset:i,startSize:g,deltaOffset:0,deltaPercentage:0,columnSizingStart:h,isResizingColumn:d.id}))}}},createTable:a=>{a.setColumnSizing=b=>null==a.options.onColumnSizingChange?void 0:a.options.onColumnSizingChange(b),a.setColumnSizingInfo=b=>null==a.options.onColumnSizingInfoChange?void 0:a.options.onColumnSizingInfoChange(b),a.resetColumnSizing=b=>{var c;a.setColumnSizing(b?{}:null!=(c=a.initialState.columnSizing)?c:{})},a.resetHeaderSizeInfo=b=>{var c;a.setColumnSizingInfo(b?B():null!=(c=a.initialState.columnSizingInfo)?c:B())},a.getTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getLeftTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getLeftHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getCenterTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getCenterHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0},a.getRightTotalSize=()=>{var b,c;return null!=(b=null==(c=a.getRightHeaderGroups()[0])?void 0:c.headers.reduce((a,b)=>a+b.getSize(),0))?b:0}}}];function R(a){var b,c;let e=[...Q,...null!=(b=a._features)?b:[]],f={_features:e},i=f._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultOptions?void 0:b.getDefaultOptions(f)),{}),j={...null!=(c=a.initialState)?c:{}};f._features.forEach(a=>{var b;j=null!=(b=null==a.getInitialState?void 0:a.getInitialState(j))?b:j});let k=[],l=!1,m={_features:e,options:{...i,...a},initialState:j,_queue:a=>{k.push(a),l||(l=!0,Promise.resolve().then(()=>{for(;k.length;)k.shift()();l=!1}).catch(a=>setTimeout(()=>{throw a})))},reset:()=>{f.setState(f.initialState)},setOptions:a=>{var b;b=d(a,f.options),f.options=f.options.mergeOptions?f.options.mergeOptions(i,b):{...i,...b}},getState:()=>f.options.state,setState:a=>{null==f.options.onStateChange||f.options.onStateChange(a)},_getRowId:(a,b,c)=>{var d;return null!=(d=null==f.options.getRowId?void 0:f.options.getRowId(a,b,c))?d:`${c?[c.id,b].join("."):b}`},getCoreRowModel:()=>(f._getCoreRowModel||(f._getCoreRowModel=f.options.getCoreRowModel(f)),f._getCoreRowModel()),getRowModel:()=>f.getPaginationRowModel(),getRow:(a,b)=>{let c=(b?f.getPrePaginationRowModel():f.getRowModel()).rowsById[a];if(!c&&!(c=f.getCoreRowModel().rowsById[a]))throw Error();return c},_getDefaultColumnDef:g(()=>[f.options.defaultColumn],a=>{var b;return a=null!=(b=a)?b:{},{header:a=>{let b=a.header.column.columnDef;return b.accessorKey?b.accessorKey:b.accessorFn?b.id:null},cell:a=>{var b,c;return null!=(b=null==(c=a.renderValue())||null==c.toString?void 0:c.toString())?b:null},...f._features.reduce((a,b)=>Object.assign(a,null==b.getDefaultColumnDef?void 0:b.getDefaultColumnDef()),{}),...a}},h(a,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>f.options.columns,getAllColumns:g(()=>[f._getColumnDefs()],a=>{let b=function(a,c,d){return void 0===d&&(d=0),a.map(a=>{let e=function(a,b,c,d){var e,f;let i,j={...a._getDefaultColumnDef(),...b},k=j.accessorKey,l=null!=(e=null!=(f=j.id)?f:k?"function"==typeof String.prototype.replaceAll?k.replaceAll(".","_"):k.replace(/\./g,"_"):void 0)?e:"string"==typeof j.header?j.header:void 0;if(j.accessorFn?i=j.accessorFn:k&&(i=k.includes(".")?a=>{let b=a;for(let a of k.split(".")){var c;b=null==(c=b)?void 0:c[a]}return b}:a=>a[j.accessorKey]),!l)throw Error();let m={id:`${String(l)}`,accessorFn:i,parent:d,depth:c,columnDef:j,columns:[],getFlatColumns:g(()=>[!0],()=>{var a;return[m,...null==(a=m.columns)?void 0:a.flatMap(a=>a.getFlatColumns())]},h(a.options,"debugColumns","column.getFlatColumns")),getLeafColumns:g(()=>[a._getOrderColumnsFn()],a=>{var b;return null!=(b=m.columns)&&b.length?a(m.columns.flatMap(a=>a.getLeafColumns())):[m]},h(a.options,"debugColumns","column.getLeafColumns"))};for(let b of a._features)null==b.createColumn||b.createColumn(m,a);return m}(f,a,d,c);return e.columns=a.columns?b(a.columns,e,d+1):[],e})};return b(a)},h(a,"debugColumns","getAllColumns")),getAllFlatColumns:g(()=>[f.getAllColumns()],a=>a.flatMap(a=>a.getFlatColumns()),h(a,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:g(()=>[f.getAllFlatColumns()],a=>a.reduce((a,b)=>(a[b.id]=b,a),{}),h(a,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:g(()=>[f.getAllColumns(),f._getOrderColumnsFn()],(a,b)=>b(a.flatMap(a=>a.getLeafColumns())),h(a,"debugColumns","getAllLeafColumns")),getColumn:a=>f._getAllFlatColumnsById()[a]};Object.assign(f,m);for(let a=0;a<f._features.length;a++){let b=f._features[a];null==b||null==b.createTable||b.createTable(f)}return f}function S(){return a=>g(()=>[a.options.data],b=>{let c={rows:[],flatRows:[],rowsById:{}},d=function(b,e,f){void 0===e&&(e=0);let g=[];for(let i=0;i<b.length;i++){let j=l(a,a._getRowId(b[i],i,f),b[i],i,e,void 0,null==f?void 0:f.id);if(c.flatRows.push(j),c.rowsById[j.id]=j,g.push(j),a.options.getSubRows){var h;j.originalSubRows=a.options.getSubRows(b[i],i),null!=(h=j.originalSubRows)&&h.length&&(j.subRows=d(j.originalSubRows,e+1,j))}}return g};return c.rows=d(b),c},h(a.options,"debugTable","getRowModel",()=>a._autoResetPageIndex()))}}};