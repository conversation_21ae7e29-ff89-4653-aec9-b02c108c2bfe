(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6388],{668:e=>{!function(){"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};t.endianness=function(){return"LE"},t.hostname=function(){return"undefined"!=typeof location?location.hostname:""},t.loadavg=function(){return[]},t.uptime=function(){return 0},t.freemem=function(){return Number.MAX_VALUE},t.totalmem=function(){return Number.MAX_VALUE},t.cpus=function(){return[]},t.type=function(){return"Browser"},t.release=function(){return"undefined"!=typeof navigator?navigator.appVersion:""},t.networkInterfaces=t.getNetworkInterfaces=function(){return{}},t.arch=function(){return"javascript"},t.platform=function(){return"browser"},t.tmpdir=t.tmpDir=function(){return"/tmp"},t.EOL="\n",t.homedir=function(){return"/"},e.exports=t}()},17691:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),i=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,i=void 0===o?24:o,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="X";let l=s},26312:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),i=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,i=void 0===o?24:o,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),n.createElement("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),n.createElement("polyline",{points:"16 17 21 12 16 7"}),n.createElement("line",{x1:"21",y1:"12",x2:"9",y2:"12"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="LogOut";let l=s},41190:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),o=r(38637),i=r.n(o);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var s=(0,n.forwardRef)(function(e,t){var r=e.color,o=e.size,i=void 0===o?24:o,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),n.createElement("circle",{cx:"12",cy:"12",r:"10"}),n.createElement("line",{x1:"2",y1:"12",x2:"22",y2:"12"}),n.createElement("path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"}))});s.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},s.displayName="Globe";let l=s},44638:(e,t,r)=>{"use strict";var n=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,s=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,c=(e,t)=>o(e,"name",{value:t,configurable:!0}),u=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))l.call(e,s)||s===r||o(e,s,{get:()=>t[s],enumerable:!(n=i(t,s))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?n(s(e)):{},u(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)),f={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(f,{default:()=>v}),e.exports=u(o({},"__esModule",{value:!0}),f);var d=p(r(38637)),h=p(r(12115)),g=p(r(76770)),m=c(e=>{let{color:t,height:r,showSpinner:n,crawl:o,crawlSpeed:i,initialPosition:a,easing:s,speed:l,shadow:u,template:p,zIndex:f=1600,showAtBottom:d=!1}=e,m=null!=t?t:"#29d",v=u||void 0===u?u?"box-shadow:".concat(u):"box-shadow:0 0 10px ".concat(m,",0 0 5px ").concat(m):"",b=h.createElement("style",null,"#nprogress{pointer-events:none}#nprogress .bar{background:".concat(m,";position:fixed;z-index:").concat(f,";").concat(d?"bottom: 0;":"top: 0;","left:0;width:100%;height:").concat(null!=r?r:3,"px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;").concat(v,";opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:").concat(f,";").concat(d?"bottom: 15px;":"top: 15px;","right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:").concat(m,";border-left-color:").concat(m,";border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}")),y=c(e=>new URL(e,window.location.href).href,"toAbsoluteURL"),w=c((e,t)=>{let r=new URL(y(e)),n=new URL(y(t));return r.href.split("#")[0]===n.href.split("#")[0]},"isHashAnchor"),E=c((e,t)=>{let r=new URL(y(e)),n=new URL(y(t));return r.hostname.replace(/^www\./,"")===n.hostname.replace(/^www\./,"")},"isSameHostName");return h.useEffect(()=>{function e(e,t){let r=new URL(e),n=new URL(t);if(r.hostname===n.hostname&&r.pathname===n.pathname&&r.search===n.search){let e=r.hash,t=n.hash;return e!==t&&r.href.replace(e,"")===n.href.replace(t,"")}return!1}g.configure({showSpinner:null==n||n,trickle:null==o||o,trickleSpeed:null!=i?i:200,minimum:null!=a?a:.08,easing:null!=s?s:"ease",speed:null!=l?l:200,template:null!=p?p:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'}),c(e,"isAnchorOfCurrentUrl");var t=document.querySelectorAll("html");let r=c(()=>t.forEach(e=>e.classList.remove("nprogress-busy")),"removeNProgressClass");function u(e){for(;e&&"a"!==e.tagName.toLowerCase();)e=e.parentElement;return e}function f(t){try{let n=t.target,o=u(n),i=null==o?void 0:o.href;if(i){let n=window.location.href,a="_blank"===o.target,s=["tel:","mailto:","sms:","blob:","download:"].some(e=>i.startsWith(e)),l=e(n,i);if(!E(window.location.href,o.href))return;i===n||l||a||s||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey||w(window.location.href,o.href)||!y(o.href).startsWith("http")?(g.start(),g.done(),r()):g.start()}}catch(e){g.start(),g.done()}}function d(){g.done(),r()}function h(){g.done()}return c(u,"findClosestAnchor"),c(f,"handleClick"),(e=>{let t=e.pushState;e.pushState=function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return g.done(),r(),t.apply(e,o)}})(window.history),(e=>{let t=e.replaceState;e.replaceState=function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return g.done(),r(),t.apply(e,o)}})(window.history),c(d,"handlePageHide"),c(h,"handleBackAndForth"),window.addEventListener("popstate",h),document.addEventListener("click",f),window.addEventListener("pagehide",d),()=>{document.removeEventListener("click",f),window.removeEventListener("pagehide",d),window.removeEventListener("popstate",h)}},[]),b},"NextTopLoader"),v=m;m.propTypes={color:d.string,height:d.number,showSpinner:d.bool,crawl:d.bool,crawlSpeed:d.number,initialPosition:d.number,easing:d.string,speed:d.number,template:d.string,shadow:d.oneOfType([d.string,d.bool]),zIndex:d.number,showAtBottom:d.bool}},71807:e=>{!function(){"use strict";var t={114:function(e){function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",o=0,i=-1,a=0,s=0;s<=e.length;++s){if(s<e.length)r=e.charCodeAt(s);else if(47===r)break;else r=47;if(47===r){if(i===s-1||1===a);else if(i!==s-1&&2===a){if(n.length<2||2!==o||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",o=0):o=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),i=s,a=0;continue}}else if(2===n.length||1===n.length){n="",o=0,i=s,a=0;continue}}t&&(n.length>0?n+="/..":n="..",o=2)}else n.length>0?n+="/"+e.slice(i+1,s):n=e.slice(i+1,s),o=s-i-1;i=s,a=0}else 46===r&&-1!==a?++a:a=-1}return n}var n={resolve:function(){for(var e,n,o="",i=!1,a=arguments.length-1;a>=-1&&!i;a--)a>=0?n=arguments[a]:(void 0===e&&(e=""),n=e),t(n),0!==n.length&&(o=n+"/"+o,i=47===n.charCodeAt(0));if(o=r(o,!i),i)if(o.length>0)return"/"+o;else return"/";return o.length>0?o:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),o=47===e.charCodeAt(e.length-1);return(0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&o&&(e+="/"),n)?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var o=arguments[r];t(o),o.length>0&&(void 0===e?e=o:e+="/"+o)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var i=e.length,a=i-o,s=1;s<r.length&&47===r.charCodeAt(s);++s);for(var l=r.length-s,c=a<l?a:l,u=-1,p=0;p<=c;++p){if(p===c){if(l>c){if(47===r.charCodeAt(s+p))return r.slice(s+p+1);else if(0===p)return r.slice(s+p)}else a>c&&(47===e.charCodeAt(o+p)?u=p:0===p&&(u=0));break}var f=e.charCodeAt(o+p);if(f!==r.charCodeAt(s+p))break;47===f&&(u=p)}var d="";for(p=o+u+1;p<=i;++p)(p===i||47===e.charCodeAt(p))&&(0===d.length?d+="..":d+="/..");return d.length>0?d+r.slice(s+u):(s+=u,47===r.charCodeAt(s)&&++s,r.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,o=-1,i=!0,a=e.length-1;a>=1;--a)if(47===(r=e.charCodeAt(a))){if(!i){o=a;break}}else i=!1;return -1===o?n?"/":".":n&&1===o?"//":e.slice(0,o)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,o=0,i=-1,a=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var s=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var c=e.charCodeAt(n);if(47===c){if(!a){o=n+1;break}}else -1===l&&(a=!1,l=n+1),s>=0&&(c===r.charCodeAt(s)?-1==--s&&(i=n):(s=-1,i=l))}return o===i?i=l:-1===i&&(i=e.length),e.slice(o,i)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!a){o=n+1;break}}else -1===i&&(a=!1,i=n+1);return -1===i?"":e.slice(o,i)},extname:function(e){t(e);for(var r=-1,n=0,o=-1,i=!0,a=0,s=e.length-1;s>=0;--s){var l=e.charCodeAt(s);if(47===l){if(!i){n=s+1;break}continue}-1===o&&(i=!1,o=s+1),46===l?-1===r?r=s:1!==a&&(a=1):-1!==r&&(a=-1)}return -1===r||-1===o||0===a||1===a&&r===o-1&&r===n+1?"":e.slice(r,o)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r,n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var o=e.charCodeAt(0),i=47===o;i?(n.root="/",r=1):r=0;for(var a=-1,s=0,l=-1,c=!0,u=e.length-1,p=0;u>=r;--u){if(47===(o=e.charCodeAt(u))){if(!c){s=u+1;break}continue}-1===l&&(c=!1,l=u+1),46===o?-1===a?a=u:1!==p&&(p=1):-1!==a&&(p=-1)}return -1===a||-1===l||0===p||1===p&&a===l-1&&a===s+1?-1!==l&&(0===s&&i?n.base=n.name=e.slice(1,l):n.base=n.name=e.slice(s,l)):(0===s&&i?(n.name=e.slice(1,a),n.base=e.slice(1,l)):(n.name=e.slice(s,a),n.base=e.slice(s,l)),n.ext=e.slice(a,l)),s>0?n.dir=e.slice(0,s-1):i&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}},a=!0;try{t[e](i,i.exports,n),a=!1}finally{a&&delete r[e]}return i.exports}n.ab="//",e.exports=n(114)}()},76770:function(e,t,r){var n,o;void 0===(o="function"==typeof(n=function(){var e,t,r,n={};n.version="0.2.0";var o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function i(e,t,r){return e<t?t:e>r?r:e}n.configure=function(e){var t,r;for(t in e)void 0!==(r=e[t])&&e.hasOwnProperty(t)&&(o[t]=r);return this},n.status=null,n.set=function(e){var t=n.isStarted();n.status=1===(e=i(e,o.minimum,1))?null:e;var r=n.render(!t),l=r.querySelector(o.barSelector),c=o.speed,u=o.easing;return r.offsetWidth,a(function(t){var i,a,p,f;""===o.positionUsing&&(o.positionUsing=n.getPositioningCSS()),s(l,(i=e,a=c,p=u,(f="translate3d"===o.positionUsing?{transform:"translate3d("+(-1+i)*100+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+(-1+i)*100+"%,0)"}:{"margin-left":(-1+i)*100+"%"}).transition="all "+a+"ms "+p,f)),1===e?(s(r,{transition:"none",opacity:1}),r.offsetWidth,setTimeout(function(){s(r,{transition:"all "+c+"ms linear",opacity:0}),setTimeout(function(){n.remove(),t()},c)},c)):setTimeout(t,c)}),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var e=function(){setTimeout(function(){n.status&&(n.trickle(),e())},o.trickleSpeed)};return o.trickle&&e(),this},n.done=function(e){return e||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(e){var t=n.status;return t?("number"!=typeof e&&(e=(1-t)*i(Math.random()*t,.1,.95)),t=i(t+e,0,.994),n.set(t)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},e=0,t=0,n.promise=function(r){return r&&"resolved"!==r.state()&&(0===t&&n.start(),e++,t++,r.always(function(){0==--t?(e=0,n.done()):n.set((e-t)/e)})),this},n.render=function(e){if(n.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=o.template;var r,i=t.querySelector(o.barSelector),a=e?"-100":(-1+(n.status||0))*100,l=document.querySelector(o.parent);return s(i,{transition:"all 0 linear",transform:"translate3d("+a+"%,0,0)"}),!o.showSpinner&&(r=t.querySelector(o.spinnerSelector))&&f(r),l!=document.body&&c(l,"nprogress-custom-parent"),l.appendChild(t),t},n.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(o.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&f(e)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective"in e?"translate3d":t+"Transform"in e?"translate":"margin"};var a=(r=[],function(e){r.push(e),1==r.length&&function e(){var t=r.shift();t&&t(e)}()}),s=function(){var e=["Webkit","O","Moz","ms"],t={};function r(r,n,o){var i;n=t[i=(i=n).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})]||(t[i]=function(t){var r=document.body.style;if(t in r)return t;for(var n,o=e.length,i=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((n=e[o]+i)in r)return n;return t}(i)),r.style[n]=o}return function(e,t){var n,o,i=arguments;if(2==i.length)for(n in t)void 0!==(o=t[n])&&t.hasOwnProperty(n)&&r(e,n,o);else r(e,i[1],i[2])}}();function l(e,t){return("string"==typeof e?e:p(e)).indexOf(" "+t+" ")>=0}function c(e,t){var r=p(e),n=r+t;l(r,t)||(e.className=n.substring(1))}function u(e,t){var r,n=p(e);l(e,t)&&(e.className=(r=n.replace(" "+t+" "," ")).substring(1,r.length-1))}function p(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function f(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return n})?n.call(t,r,t,e):n)||(e.exports=o)},86589:e=>{"use strict";e.exports=JSON.parse('{"name":"dotenv","version":"16.6.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')},97967:(e,t,r)=>{var n=r(49509),o=r(44134).Buffer;let i=r(72016),a=r(71807),s=r(668),l=r(8777),c=r(86589).version,u=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function p(e){console.log(`[dotenv@${c}][DEBUG] ${e}`)}function f(e){console.log(`[dotenv@${c}] ${e}`)}function d(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:n.env.DOTENV_KEY&&n.env.DOTENV_KEY.length>0?n.env.DOTENV_KEY:""}function h(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)i.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=a.resolve(n.cwd(),".env.vault");return i.existsSync(t)?t:null}function g(e){return"~"===e[0]?a.join(s.homedir(),e.slice(1)):e}let m={configDotenv:function(e){let t,r=a.resolve(n.cwd(),".env"),o="utf8",s=!!(e&&e.debug),l=!e||!("quiet"in e)||e.quiet;e&&e.encoding?o=e.encoding:s&&p("No encoding is specified. UTF-8 is used by default");let c=[r];if(e&&e.path)if(Array.isArray(e.path))for(let t of(c=[],e.path))c.push(g(t));else c=[g(e.path)];let u={};for(let r of c)try{let t=m.parse(i.readFileSync(r,{encoding:o}));m.populate(u,t,e)}catch(e){s&&p(`Failed to load ${r} ${e.message}`),t=e}let d=n.env;if(e&&null!=e.processEnv&&(d=e.processEnv),m.populate(d,u,e),s||!l){let e=Object.keys(u).length,r=[];for(let e of c)try{let t=a.relative(n.cwd(),e);r.push(t)}catch(r){s&&p(`Failed to load ${e} ${r.message}`),t=r}f(`injecting env (${e}) from ${r.join(",")}`)}return t?{parsed:u,error:t}:{parsed:u}},_configVault:function(e){let t=!!(e&&e.debug),r=!e||!("quiet"in e)||e.quiet;(t||!r)&&f("Loading env from encrypted .env.vault");let o=m._parseVault(e),i=n.env;return e&&null!=e.processEnv&&(i=e.processEnv),m.populate(i,o,e),{parsed:o}},_parseVault:function(e){let t,r=h(e=e||{});e.path=r;let n=m.configDotenv(e);if(!n.parsed){let e=Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);throw e.code="MISSING_DATA",e}let o=d(e).split(","),i=o.length;for(let e=0;e<i;e++)try{let r=o[e].trim(),i=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=r.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let o=r.searchParams.get("environment");if(!o){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let i=`DOTENV_VAULT_${o.toUpperCase()}`,a=e.parsed[i];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${i} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:n}}(n,r);t=m.decrypt(i.ciphertext,i.key);break}catch(t){if(e+1>=i)throw t}return m.parse(t)},config:function(e){if(0===d(e).length)return m.configDotenv(e);let t=h(e);if(!t){var r;return r=`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`,console.log(`[dotenv@${c}][WARN] ${r}`),m.configDotenv(e)}return m._configVault(e)},decrypt:function(e,t){let r=o.from(t.slice(-64),"hex"),n=o.from(e,"base64"),i=n.subarray(0,12),a=n.subarray(-16);n=n.subarray(12,-16);try{let e=l.createDecipheriv("aes-256-gcm",r,i);return e.setAuthTag(a),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,r="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,r={},n=e.toString();for(n=n.replace(/\r\n?/mg,"\n");null!=(t=u.exec(n));){let e=t[1],n=t[2]||"",o=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===o&&(n=(n=n.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),r[e]=n}return r},populate:function(e,t,r={}){let n=!!(r&&r.debug),o=!!(r&&r.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===o&&(e[r]=t[r]),n&&(!0===o?p(`"${r}" is already defined and WAS overwritten`):p(`"${r}" is already defined and was NOT overwritten`))):e[r]=t[r]}};e.exports.configDotenv=m.configDotenv,e.exports._configVault=m._configVault,e.exports._parseVault=m._parseVault,e.exports.config=m.config,e.exports.decrypt=m.decrypt,e.exports.parse=m.parse,e.exports.populate=m.populate,e.exports=m}}]);