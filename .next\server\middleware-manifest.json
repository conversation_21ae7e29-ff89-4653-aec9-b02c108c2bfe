{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/register(\\.json)?[\\/#\\?]?$", "originalSource": "/register"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/change-password(\\.json)?[\\/#\\?]?$", "originalSource": "/change-password"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/forgot-pass(\\.json)?[\\/#\\?]?$", "originalSource": "/forgot-pass"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "u37QtfSAQLmu4DND3U0mF", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "R0Z8GfOSDQiw7OgagAoprXwBxrqH5znGcY4x2up+tqg=", "__NEXT_PREVIEW_MODE_ID": "1a70de13a293b03537d62b2604426b0d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "01dd677be790bd5e06021eab7cfb0db1a4dfca6f53df984372188f69754e0dd5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e1414a2e60f07317a6811e3f323a97d3e3e3df844b3aac8dc8cb492c786989f"}}}, "functions": {}, "sortedMiddleware": ["/"]}