exports.id=5600,exports.ids=[5600],exports.modules={1903:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(60687),e=c(45671),f=c(76634),g=c(45434);function h({className:a=""}){let{toggleMobileMenu:b,isMobileOpen:c}=(0,f.c)();return(0,d.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),b()},className:`md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors ${a}`,"aria-label":"Toggle mobile menu",type:"button",children:(0,d.jsx)(g.A,{className:"w-6 h-6 text-gray-600"})})}var i=c(16189);let j=()=>{let a=(0,i.usePathname)();return(0,d.jsx)("header",{className:"w-full bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40",children:(0,d.jsx)("div",{className:"px-6 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(h,{}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"/dashboard"===a?"Tổng quan":a.includes("/user")?"Quản l\xfd th\xe0nh vi\xean":a.includes("/files")?"Quản l\xfd file":a.includes("/court-cases")?"Quản l\xfd vụ việc t\xf2a \xe1n":a.includes("/setting")?"C\xe0i đặt":a.includes("/account")?"Th\xf4ng tin t\xe0i khoản":"Dashboard"}),(0,d.jsx)("div",{className:"flex items-center text-sm text-gray-500 mt-1",children:(0,d.jsx)("span",{children:"Dashboard"})})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsx)(e.A,{})})]})})})}},8178:(a,b,c)=>{"use strict";c.d(b,{default:()=>B});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(30474),i=c(61118),j=c(13327),k=c(54307);let l=()=>{let{setting:a,loading:b}=(0,i.i)(),{hasPermission:c}=(0,j.A)();return b?(0,d.jsx)("div",{className:"flex items-center space-x-3",children:(0,d.jsx)("div",{className:"h-12 w-32 bg-gray-200 rounded animate-pulse"})}):(0,d.jsx)(f(),{href:"/dashboard",className:"flex items-center space-x-3 rtl:space-x-reverse hover:opacity-80 transition-opacity",children:a?.logo?.path?(0,d.jsx)(h.default,{src:`${k.A.NEXT_PUBLIC_API_ENDPOINT}/${a.logo.path}`,alt:"Site Logo",width:300,height:200,quality:100,priority:!0,className:"max-w-32 max-h-12 object-contain"}):(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-sm",children:"D"})}),(0,d.jsx)("span",{className:"text-lg font-bold text-gray-800",children:"Dashboard"})]})})};var m=c(84572),n=c(55109),o=c(43210),p=c(76634),q=c(80508),r=c(76957),s=c(94652),t=c(73543),u=c(70628),v=c(5980),w=c(50874),x=c(43471),y=c(27973),z=c(37150),A=c(5241);function B(){let{hasPermission:a,user:b}=(0,j.A)(),{hasPermission:c,isAdmin:e}=(0,n.S)(),{isCollapsed:h,isMobileOpen:i,toggleCollapse:k,closeMobileMenu:B}=(0,p.c)(),C=(0,g.usePathname)();C.startsWith("/dashboard");let[D,E]=(0,o.useState)([]);(0,o.useRef)(C);let F=({icon:a,className:b=""})=>(0,d.jsx)(a,{size:18,className:b}),G=[],H=[{permission:"admin",title:"Quản L\xfd Ph\xf2ng Ban",href:"/dashboard/departments"},{permission:"admin",title:"Th\xeam Ph\xf2ng Ban",href:"/dashboard/departments/add"}].filter(b=>a(b.permission)),I=b?.rule==="department_manager";if(H.length>0||I){let b=[];a("admin")?b.push({title:"Quản L\xfd Ph\xf2ng Ban",href:"/dashboard/departments"},{title:"Th\xeam Ph\xf2ng Ban",href:"/dashboard/departments/add"}):I&&b.push({title:"Quản L\xfd Ph\xf2ng Ban",href:"/dashboard/departments"}),b.length>0&&G.push({title:"Ph\xf2ng Ban",href:"/dashboard/departments",icon:q.A,children:b})}let J=[{permission:"user_view",title:"Quản L\xfd Người D\xf9ng",href:"/dashboard/user"},{permission:"user_add",title:"Th\xeam Người D\xf9ng",href:"/dashboard/user/add"},{permission:"user_import_csv",title:"Nhập File CSV",href:"/dashboard/user/import"}].filter(a=>c(a.permission));J.length>0&&G.push({title:"Th\xe0nh Vi\xean",href:"/dashboard/user",icon:r.A,children:J.map(a=>({title:a.title,href:a.href}))}),[{permission:"file_view",title:"Xem File",href:"/dashboard/files"},{permission:"file_upload",title:"Upload File",href:"/dashboard/files"},{permission:"file_delete",title:"Quản L\xfd File",href:"/dashboard/files"}].some(a=>c(a.permission))&&G.push({title:"Quản L\xfd File",href:"/dashboard/files",icon:s.A,children:[{title:"Quản L\xfd File",href:"/dashboard/files"}]});let K=[{permission:"court_case_view",title:"Xem Vụ Việc",href:"/dashboard/court-cases"},{permission:"court_case_create",title:"Th\xeam Vụ Việc",href:"/dashboard/court-cases"},{permission:"court_case_edit",title:"Sửa Vụ Việc",href:"/dashboard/court-cases"},{permission:"court_case_delete",title:"X\xf3a Vụ Việc",href:"/dashboard/court-cases"}].some(a=>c(a.permission));K&&G.push({title:"Quản L\xfd Vụ Việc",href:"/dashboard/court-cases",icon:t.A,children:[{title:"Danh S\xe1ch Vụ Việc",href:"/dashboard/court-cases"}]}),(c("system_settings_view")||c("system_settings_edit"))&&G.push({title:"C\xe0i đặt",href:"/dashboard/setting",icon:u.A}),G.push({title:"Tổng quan",href:"/dashboard",icon:v.A});let L=[{title:"Chỉnh sửa th\xf4ng tin",href:"/dashboard/account",icon:w.A}],M=[];(c("system_settings_view")||c("system_settings_edit"))&&M.push({title:"C\xe0i đặt",href:"/dashboard/manager/setting",icon:u.A}),(c("file_view")||c("file_upload")||c("file_delete"))&&M.push({title:"Quản L\xfd File",href:"/dashboard/files",icon:s.A}),K&&M.push({title:"Quản L\xfd Vụ Việc",href:"/dashboard/court-cases",icon:t.A});let N=({item:a,isChild:b=!1})=>{var c;let e,g=a.children&&a.children.length>0,i="/dashboard"===(c=a.href)?"/dashboard"===C:C===c||C.startsWith(c+"/"),j=(e=a.href,!h&&D.includes(e));return(0,d.jsxs)("li",{className:`${b?h?"":"ml-4":""}`,children:[(0,d.jsx)("div",{className:"flex items-center",children:g&&!h?(0,d.jsxs)("button",{onClick:()=>{var b;return b=a.href,void(!h&&E(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b]))},className:`flex items-center w-full px-4 py-3 text-left rounded-lg transition-all duration-200 group ${i?"bg-blue-600 text-white shadow-lg":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,title:h?a.title:"",children:[a.icon&&(0,d.jsx)(F,{icon:a.icon,className:`${h?"mx-auto":"mr-3"} ${i?"text-white":"text-gray-500"}`}),!h&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"flex-1 font-medium",children:a.title}),(0,d.jsx)(F,{icon:j?x.A:y.A,className:`ml-2 ${i?"text-white":"text-gray-400"}`})]})]}):(0,d.jsxs)(f(),{href:a.href,className:`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 group ${i?"bg-blue-600 text-white shadow-lg":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,title:h?a.title:"",children:[a.icon&&(0,d.jsx)(F,{icon:a.icon,className:`${h?"mx-auto":"mr-3"} ${i?"text-white":"text-gray-500"}`}),!h&&(0,d.jsx)("span",{className:"font-medium",children:a.title})]})}),g&&j&&!h&&(0,d.jsx)("ul",{className:"mt-2 space-y-1 ml-4",children:a.children.map(a=>(0,d.jsx)("li",{children:(0,d.jsxs)(f(),{href:a.href,className:`flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${C===a.href?"bg-blue-50 text-blue-700 border-l-4 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,d.jsx)("span",{className:"w-2 h-2 bg-gray-300 rounded-full mr-3"}),a.title]})},a.href))})]})};return(0,d.jsxs)(d.Fragment,{children:[i&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:a=>{a.preventDefault(),a.stopPropagation(),B()}}),(0,d.jsxs)("nav",{className:`
          fixed top-0 bottom-0 left-0 z-50 flex flex-col
          bg-white shadow-xl border-r border-gray-200
          transform transition-all duration-300 ease-in-out
          overflow-hidden
          md:translate-x-0
          ${i?"translate-x-0":"-translate-x-full md:translate-x-0"}
          ${h?"md:w-16":"md:w-64"}
          w-64
        `,onClick:a=>a.stopPropagation(),children:[(0,d.jsxs)("div",{className:`flex items-center justify-between p-4 border-b border-gray-200 ${h?"px-2":"px-6"}`,children:[!h&&(0,d.jsx)(l,{}),(0,d.jsx)("button",{onClick:k,className:"hidden md:flex p-2 rounded-lg hover:bg-gray-100 transition-colors",title:h?"Mở rộng sidebar":"Thu gọn sidebar",children:(0,d.jsx)(z.A,{className:`w-5 h-5 text-gray-500 transition-transform ${h?"rotate-180":""}`})}),(0,d.jsx)("button",{onClick:B,className:"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors",children:(0,d.jsx)(A.A,{className:"w-5 h-5 text-gray-500"})})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto py-4 px-4",children:e||G.length>0||b?.rule==="department_manager"?(0,d.jsxs)("div",{className:"space-y-2",children:[G.map(a=>(0,d.jsx)(N,{item:a},a.href)),(0,d.jsx)("div",{className:`pt-4 mt-6 border-t border-gray-200 ${h?"border-t-0 mt-4 pt-2":""}`,children:(0,d.jsx)(N,{item:{title:"Th\xf4ng tin t\xe0i khoản",href:"/dashboard/account",icon:w.A}})})]}):(0,d.jsxs)("div",{className:"space-y-2",children:[L.map(a=>(0,d.jsx)(N,{item:a},a.href)),a("user")&&[].map(a=>(0,d.jsx)(N,{item:a},a.href)),a("manager")&&M.map(a=>(0,d.jsx)(N,{item:a},a.href))]})}),(0,d.jsx)("div",{className:`p-4 border-t border-gray-200 ${h?"px-2":""}`,children:(0,d.jsx)("div",{className:h?"flex justify-center":"",children:(0,d.jsx)(m.A,{compact:h})})})]})]})}},23986:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\Navigation\\\\SecretHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Navigation\\SecretHeader.tsx","default")},29086:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(76634);function f({children:a}){let{isCollapsed:b}=(0,e.c)();return(0,d.jsx)("div",{className:`
        transition-all duration-300 min-h-screen
        ${b?"md:ml-16":"md:ml-64"}
        ml-0
      `,children:a})}},46954:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\SideMenu.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\SideMenu.tsx","default")},55109:(a,b,c)=>{"use strict";c.d(b,{S:()=>e});var d=c(78314);let e=()=>{let{user:a,isLoading:b}=(0,d.U)();return{hasPermission:c=>!b&&!!a&&("admin"===a.rule||a.permissions?.includes(c)||!1),hasAnyPermission:c=>!b&&!!a&&("admin"===a.rule||c.some(b=>a.permissions?.includes(b))),getAllPermissions:()=>b||!a?[]:"admin"===a.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:a.permissions||[],userPermissions:a?.permissions||[],isAdmin:!b&&a?.rule==="admin",isDepartmentManager:!b&&a?.rule==="department_manager",isLoading:b}}},62344:(a,b,c)=>{"use strict";c.d(b,{SidebarProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SidebarContext.tsx","useSidebar");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SidebarContext.tsx","SidebarProvider")},69836:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,76562,23)),Promise.resolve().then(c.bind(c,78314)),Promise.resolve().then(c.bind(c,29086)),Promise.resolve().then(c.bind(c,1903)),Promise.resolve().then(c.bind(c,8178)),Promise.resolve().then(c.bind(c,61118)),Promise.resolve().then(c.bind(c,76634))},75582:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(37413),e=c(54420),f=c(68088),g=c(62344),h=c(51240),i=c.n(h),j=c(46954);let k=()=>(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("footer",{className:"bg-white py-4 border-t border-gray-300 mt-4 px-4",children:(0,d.jsx)("div",{className:"copyright flex flex-wrap items-center justify-between",children:(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:"\xa9 2025. To\xe0n bộ bản quyền thuộc t\xf2a \xe1n nh\xe2n d\xe2n Hồ Ch\xed Minh"})})})})});var l=c(23986),m=c(90768);async function n({children:a}){return(0,d.jsx)(e.default,{children:(0,d.jsx)(f.SettingProvider,{children:(0,d.jsx)(g.SidebarProvider,{children:(0,d.jsxs)("div",{className:"main-private bg-gray-50 min-h-screen",children:[(0,d.jsx)(j.default,{}),(0,d.jsxs)(m.default,{children:[(0,d.jsx)(l.default,{}),(0,d.jsx)("main",{className:"relative",children:(0,d.jsxs)("div",{className:"mx-auto w-full",children:[(0,d.jsx)("div",{className:"px-6 py-8 md:px-8",children:a}),(0,d.jsx)(k,{})]})})]}),(0,d.jsx)(i(),{color:"#3B82F6",height:3,showSpinner:!1})]})})})})}},76634:(a,b,c)=>{"use strict";c.d(b,{SidebarProvider:()=>h,c:()=>g});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0),g=()=>{let a=(0,e.useContext)(f);if(!a)throw Error("useSidebar must be used within a SidebarProvider");return a},h=({children:a})=>{let[b,c]=(0,e.useState)(!1),[g,h]=(0,e.useState)(!1);return(0,d.jsx)(f.Provider,{value:{isCollapsed:b,isMobileOpen:g,toggleCollapse:()=>{c(!b)},toggleMobileMenu:()=>{h(a=>!a)},closeMobileMenu:()=>{h(!1)}},children:a})}},90768:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\Layout\\\\DynamicLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Layout\\DynamicLayout.tsx","default")},99340:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,51240,23)),Promise.resolve().then(c.bind(c,54420)),Promise.resolve().then(c.bind(c,90768)),Promise.resolve().then(c.bind(c,23986)),Promise.resolve().then(c.bind(c,46954)),Promise.resolve().then(c.bind(c,68088)),Promise.resolve().then(c.bind(c,62344))}};