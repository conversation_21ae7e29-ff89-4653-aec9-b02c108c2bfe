(()=>{var a={};a.id=2659,a.ids=[2659],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16500:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={createDepartment:(a,b)=>d.Ay.post("/api/departments",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartments:(a,b)=>d.Ay.post("/api/departments/list",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartmentById:(a,b)=>d.Ay.get(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateDepartment:(a,b,c)=>d.Ay.put(`/api/departments/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteDepartment:(a,b)=>d.Ay.delete(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),addMemberToDepartment:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members`,b,{headers:{Authorization:`Bearer ${c}`}}),getDepartmentMembers:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members/list`,b,{headers:{Authorization:`Bearer ${c}`}}),updateMemberPermissions:(a,b,c,e)=>d.Ay.put(`/api/departments/${a}/members/${b}`,c,{headers:{Authorization:`Bearer ${e}`}}),removeMemberFromDepartment:(a,b,c)=>d.Ay.delete(`/api/departments/${a}/members/${b}`,{headers:{Authorization:`Bearer ${c}`}}),getAvailablePermissions:a=>d.Ay.get("/api/departments/permissions",{headers:{Authorization:`Bearer ${a}`}})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25524:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("polyline",{points:"3 6 5 6 21 6"}),e().createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),e().createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),e().createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Trash2";let j=i},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43956:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),e().createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),e().createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Save";let j=i},44047:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\departments\\\\[id]\\\\members\\\\[memberId]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx","default")},48112:(a,b,c)=>{Promise.resolve().then(c.bind(c,44047))},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71170:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(60687);c(43210);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},f=c[a]||c.user;return(0,d.jsx)(e,{variant:f.variant,className:b,children:f.label})}},78440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["departments",{children:["[id]",{children:["members",{children:["[memberId]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,44047)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/departments/[id]/members/[memberId]/edit/page",pathname:"/dashboard/departments/[id]/members/[memberId]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/departments/[id]/members/[memberId]/edit/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79428:a=>{"use strict";a.exports=require("buffer")},82960:(a,b,c)=>{Promise.resolve().then(c.bind(c,86983))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86983:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w});var d=c(60687),e=c(43210),f=c.n(e),g=c(16189),h=c(93853),i=c(16500),j=c(88577),k=c(98462),l=c(96800),m=c(50874),n=c(87955),o=c.n(n);function p(){return(p=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var q=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",p({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),f().createElement("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),f().createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"}))});function r(){return(r=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}q.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},q.displayName="AlertTriangle";var s=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",r({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"}))});s.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},s.displayName="Key";var t=c(25524),u=c(43956),v=c(71170);function w(){let a=(0,g.useRouter)(),b=(0,g.useParams)(),c=b.id,f=b.memberId,[n,o]=(0,e.useState)(!1),[p,r]=(0,e.useState)(!0),[w,x]=(0,e.useState)([]),[y,z]=(0,e.useState)(null),[A,B]=(0,e.useState)({permissions:[],departmentRole:"member"}),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(""),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)(!1),M=async b=>{b.preventDefault();try{o(!0);let b=localStorage.getItem("sessionToken")||"",d=await i.A.updateMemberPermissions(c,f,A,b);d.payload.success?(h.oR.success("Cập nhật quyền th\xe0nh vi\xean th\xe0nh c\xf4ng"),a.push(`/dashboard/departments/${c}`)):h.oR.error(d.payload.message||"Kh\xf4ng thể cập nhật quyền th\xe0nh vi\xean")}catch(a){console.error("Error updating member permissions:",a),h.oR.error("Lỗi khi cập nhật quyền th\xe0nh vi\xean")}finally{o(!1)}},N=async()=>{try{J(!0);let b=localStorage.getItem("sessionToken")||"",d=await i.A.removeMemberFromDepartment(c,f,b);d.payload.success?(h.oR.success("X\xf3a th\xe0nh vi\xean khỏi ph\xf2ng ban th\xe0nh c\xf4ng"),a.push(`/dashboard/departments/${c}`)):h.oR.error(d.payload.message||"Kh\xf4ng thể x\xf3a th\xe0nh vi\xean")}catch(a){console.error("Error deleting member:",a),h.oR.error("Lỗi khi x\xf3a th\xe0nh vi\xean")}finally{J(!1),D(!1)}},O=async()=>{if(!G.trim())return void h.oR.error("Vui l\xf2ng nhập mật khẩu mới");try{L(!0);let a=localStorage.getItem("sessionToken")||"",b=await j.A.updateUser({userId:f,password:G},a);b.payload.success?(h.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng"),F(!1),H("")):h.oR.error(b.payload.message||"Kh\xf4ng thể đổi mật khẩu")}catch(a){console.error("Error resetting password:",a),h.oR.error("Lỗi khi đổi mật khẩu")}finally{L(!1)}},P=w.reduce((a,b)=>(a[b.category]||(a[b.category]=[]),a[b.category].push(b),a),{});return p?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):y?(0,d.jsx)(k.default,{requiredPermission:"admin",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,d.jsx)("button",{onClick:()=>a.push(`/dashboard/departments/${c}`),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(l.A,{size:20})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa quyền th\xe0nh vi\xean"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Cập nhật quyền hạn cho th\xe0nh vi\xean ph\xf2ng ban"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(m.A,{size:20}),"Th\xf4ng tin th\xe0nh vi\xean"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xean người d\xf9ng"}),(0,d.jsx)("p",{className:"text-gray-900 font-medium",children:y.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,d.jsx)("p",{className:"text-gray-900",children:y.email})]}),y.phonenumber&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),(0,d.jsx)("p",{className:"text-gray-900",children:y.phonenumber})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vai tr\xf2 hiện tại"}),(0,d.jsx)(v.Ex,{variant:"info",children:"manager"===y.departmentRole?"Quản l\xfd ph\xf2ng ban":"Th\xe0nh vi\xean"})]})]})]}),(0,d.jsxs)("form",{onSubmit:M,className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"radio",name:"departmentRole",value:"member",checked:"member"===A.departmentRole,onChange:a=>B(b=>({...b,departmentRole:a.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Th\xe0nh vi\xean"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Th\xe0nh vi\xean thường của ph\xf2ng ban"})]})]}),(0,d.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"radio",name:"departmentRole",value:"manager",checked:"manager"===A.departmentRole,onChange:a=>B(b=>({...b,departmentRole:a.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["C\xf3 quyền quản l\xfd th\xe0nh vi\xean trong ph\xf2ng ban","department_manager"===y.rule&&"manager"===A.departmentRole&&(0,d.jsx)("span",{className:"text-green-600 ml-1",children:"(Đang l\xe0 quản l\xfd)"}),"department_manager"===y.rule&&"manager"!==A.departmentRole&&(0,d.jsx)("span",{className:"text-orange-600 ml-1",children:"(Sẽ bị hạ cấp)"})]})]})]})]}),"department_manager"===y.rule&&"manager"!==A.departmentRole&&(0,d.jsx)("div",{className:"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(q,{size:20,className:"text-orange-500 mt-0.5 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-orange-800 mb-1",children:"Cảnh b\xe1o thay đổi vai tr\xf2"}),(0,d.jsxs)("p",{className:"text-sm text-orange-700",children:["Th\xe0nh vi\xean n\xe0y hiện đang l\xe0 ",(0,d.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),'. Nếu thay đổi về "Th\xe0nh vi\xean", họ sẽ mất quyền quản l\xfd ph\xf2ng ban v\xe0 vai tr\xf2 hệ thống sẽ được thay đổi về "Th\xe0nh vi\xean ph\xf2ng ban".']})]})]})}),"manager"===A.departmentRole&&"department_manager"!==y.rule&&(0,d.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(q,{size:20,className:"text-blue-500 mt-0.5 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:"Th\xf4ng b\xe1o thăng cấp"}),(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:["Th\xe0nh vi\xean n\xe0y sẽ được thăng cấp th\xe0nh ",(0,d.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),". Nếu ph\xf2ng ban đ\xe3 c\xf3 quản l\xfd kh\xe1c, họ sẽ tự động bị hạ cấp về th\xe0nh vi\xean."]})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền hạn cụ thể"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Chọn c\xe1c quyền cụ thể cho th\xe0nh vi\xean n\xe0y (bổ sung v\xe0o quyền mặc định của ph\xf2ng ban)"}),(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(P).map(([a,b])=>(0,d.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:a}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:b.map(a=>(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:A.permissions.includes(a.key),onChange:b=>{var c,d;return c=a.key,d=b.target.checked,void B(a=>({...a,permissions:d?[...a.permissions,c]:a.permissions.filter(a=>a!==c)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:a.name})]},a.key))})]},a))}),(0,d.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,d.jsx)("strong",{children:"Lưu \xfd:"})," Th\xe0nh vi\xean sẽ c\xf3 tổng cộng ",A.permissions.length," quyền được chọn cộng với c\xe1c quyền mặc định của ph\xf2ng ban."]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(q,{size:20,className:"text-orange-500"}),"Quản l\xfd t\xe0i khoản"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"border border-blue-200 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,d.jsx)(s,{size:16,className:"text-blue-500"}),"Đổi mật khẩu"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Đặt lại mật khẩu cho th\xe0nh vi\xean n\xe0y"}),E?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("input",{type:"password",value:G,onChange:a=>H(a.target.value),placeholder:"Nhập mật khẩu mới",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{type:"button",onClick:O,disabled:K,className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:K?"Đang cập nhật...":"Cập nhật"}),(0,d.jsx)("button",{type:"button",onClick:()=>{F(!1),H("")},className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,d.jsx)("button",{type:"button",onClick:()=>F(!0),className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:"Đổi mật khẩu"})]}),(0,d.jsxs)("div",{className:"border border-red-200 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,d.jsx)(t.A,{size:16,className:"text-red-500"}),"X\xf3a khỏi ph\xf2ng ban"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"X\xf3a th\xe0nh vi\xean n\xe0y khỏi ph\xf2ng ban (kh\xf4ng x\xf3a t\xe0i khoản)"}),C?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"p-3 bg-red-50 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-red-800",children:[(0,d.jsx)("strong",{children:"Cảnh b\xe1o:"})," Th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban v\xe0 mất tất cả quyền li\xean quan. T\xe0i khoản vẫn tồn tại nhưng kh\xf4ng thuộc ph\xf2ng ban n\xe0o."]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{type:"button",onClick:N,disabled:I,className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:I?"Đang x\xf3a...":"X\xe1c nhận x\xf3a"}),(0,d.jsx)("button",{type:"button",onClick:()=>D(!1),className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,d.jsx)("button",{type:"button",onClick:()=>D(!0),className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors",children:"X\xf3a khỏi ph\xf2ng ban"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,d.jsx)("button",{type:"button",onClick:()=>a.push(`/dashboard/departments/${c}`),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsxs)("button",{type:"submit",disabled:n,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,d.jsx)(u.A,{size:16}),n?"Đang cập nhật...":"Cập nhật quyền"]})]})]})]})}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy th\xe0nh vi\xean"})})}},88577:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={fetchUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),getAllUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),fetchLogs:(a,b)=>d.Ay.get(`api/administrator/log/${a}`,{headers:{Authorization:`Bearer ${b}`}}),deleteUser:(a,b)=>d.Ay.delete(`api/administrator/users/${a._id}`,{headers:{Authorization:`Bearer ${b}`}}),fetchUserById:(a,b,c)=>d.Ay.get(`api/administrator/users/${a}`,{headers:{Authorization:`Bearer ${b}`},signal:c}),CreateUser:(a,b)=>d.Ay.post("api/administrator/signup",a,{headers:{Authorization:`Bearer ${b}`}}),updateUser:(a,b)=>d.Ay.put("api/administrator/change-info/",a,{headers:{Authorization:`Bearer ${b}`}}),updatePassUser:(a,b)=>d.Ay.put("api/administrator/users/change-pass/",a,{headers:{Authorization:`Bearer ${b}`}})}},96800:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),e().createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="ArrowLeft";let j=i},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isDepartmentManager:l,isLoading:m}=(0,e.S)(),n=(0,f.useRouter)();if(m)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?"admin"===b&&!!l||i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>n.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,9377,5600],()=>b(b.s=78440));module.exports=c})();