(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{6821:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(95155);s(12115);var l=s(35695),n=s(99843);let a=()=>{let{setting:e,menus:t}=(0,n.i)(),s=(0,l.usePathname)().startsWith("/dashboard"),a=null==t?void 0:t.find(e=>"7"===e.position);return(0,r.jsx)(r.Fragment,{children:!s&&(0,r.jsxs)("footer",{className:"mx-auto border-t border-gray-300",children:[(0,r.jsx)("div",{className:"bg-[#e4393c] text-white",children:(0,r.jsx)("div",{className:"container mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"copyright flex flex-wrap items-center justify-between",children:[(0,r.jsx)("div",{className:"mb-4 md:mb-0",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3",children:(0,r.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",className:"w-12 h-12"})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"text-sm text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:"T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH"})]})]})}),a&&(0,r.jsx)("div",{className:"right flex flex-wrap gap-4",children:a.obj.map(e=>(0,r.jsx)("a",{href:"/".concat(e.slug),className:"hover:text-blue-200 transition-colors",children:e.text},e.id))})]})})}),(0,r.jsx)("div",{className:"bg-[#ca1619]",children:(0,r.jsx)("div",{className:"container mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"copyright-footer grid md:grid-cols-3 grid-cols-1 gap-8",children:[(null==e?void 0:e.footerBLock1)&&(0,r.jsx)("div",{className:"text-white font-normal",children:(0,r.jsx)("div",{className:"footer-content",dangerouslySetInnerHTML:{__html:e.footerBLock1}})}),(null==e?void 0:e.footerBLock2)&&(0,r.jsx)("div",{className:"text-white font-normal",children:(0,r.jsx)("div",{className:"footer-content",dangerouslySetInnerHTML:{__html:e.footerBLock2}})}),(0,r.jsx)("div",{className:"flex items-center justify-start md:justify-end",children:(0,r.jsx)("p",{className:"text-white font-normal",children:(null==e?void 0:e.copyright)||"\xa9 Your Website Name"})})]})})})]})})}},36109:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(95155),l=s(35695),n=s(12115),a=s(17691),i=s(6874),d=s.n(i),o=s(99843),c=s(66474),m=s(41190),h=s(77713);function x(){let[e,t]=(0,n.useState)(""),s=(0,l.useRouter)();return(0,r.jsxs)("form",{className:"mx-auto",onSubmit:r=>{r.preventDefault(),e.trim()&&(s.push("/search?q=".concat(encodeURIComponent(e))),t(""))},children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-900 sr-only dark:text-white",children:"Search"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none",children:(0,r.jsx)(h.A,{className:"w-4 h-4 text-gray-500 dark:text-gray-400"})}),(0,r.jsx)("input",{type:"search",id:"default-search",className:"block w-full rounded-xl p-2 ps-8 text-sm text-gray-900 border border-gray-300 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:"T\xecm kiếm",value:e,onChange:e=>t(e.target.value),required:!0})]})]})}let u=()=>{let{menus:e}=(0,o.i)(),[t,s]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{let e=()=>{let e=document.getElementById("nav");if(!e)return;let t=e.offsetTop+e.offsetHeight;(window.scrollY||window.pageYOffset)>=t?s(!0):s(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),!e)return(0,r.jsx)("div",{className:"border-t border-red-800 bottom-nav text-white",style:{backgroundColor:"#e4393c"},children:(0,r.jsx)("div",{className:"container mx-auto",children:(0,r.jsx)("nav",{id:"nav",className:"w-full ".concat(t?"sticky top-0 shadow-md z-50":""),style:{backgroundColor:"#e4393c"},children:(0,r.jsx)("div",{className:"flex w-full animate-pulse",children:Array.from({length:6}).map((e,t)=>(0,r.jsx)("div",{className:"flex-1 border-r border-red-800 last:border-r-0 h-10 py-2 px-4",children:(0,r.jsx)("div",{className:"h-full bg-red-700 rounded"})},t))})})})});if(!Array.isArray(e))return null;let l=e.find(e=>"0"===e.position);if(!l||!l.obj)return null;let a=l.obj.reduce((e,t)=>(e[t.parent]=e[t.parent]||[],e[t.parent].push(t),e),{}),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return a[e]?t?(0,r.jsx)("ul",{className:"sub-menu dropdown-content text-white z-[1] w-52 p-2 shadow rounded border-t border-red-700",style:{backgroundColor:"#d13438"},children:a[e].map(e=>(0,r.jsx)("li",{className:"relative my-2 cursor-pointer hover:bg-red-800",children:a[e.id]?(0,r.jsxs)("div",{className:"dropdown dropdown-bottom dropdown-hover",children:[(0,r.jsxs)("div",{tabIndex:0,className:"cursor-pointer flex items-center",children:[(0,r.jsx)(d(),{href:"/".concat(e.slug),className:"block w-full",children:(0,r.jsx)("span",{className:"whitespace-nowrap uppercase",children:e.text})}),(0,r.jsx)(c.A,{className:"w-4 h-4 text-white ml-1 hidden md:flex",size:"12"})]}),i(e.id,!0)]}):(0,r.jsx)(d(),{href:"/".concat(e.slug),className:"block w-full",children:(0,r.jsx)("span",{className:"whitespace-nowrap uppercase",children:e.text})})},e.id))}):(0,r.jsxs)("div",{className:"flex w-full",children:[(0,r.jsx)(d(),{href:"/",className:"flex-none border-r",style:{backgroundColor:"#e4393c"},children:(0,r.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-2",children:(0,r.jsx)(m.A,{size:"18",className:"text-white"})})}),a[e].map(e=>(0,r.jsx)("div",{className:"text-center border-r",style:{backgroundColor:"#e4393c"},children:a[e.id]?(0,r.jsxs)("div",{className:"dropdown dropdown-bottom dropdown-hover h-full",children:[(0,r.jsxs)("div",{tabIndex:0,className:"h-full cursor-pointer flex justify-center items-center px-2 py-2 hover:bg-red-700",children:[(0,r.jsx)(d(),{href:"/".concat(e.slug),className:"block",children:(0,r.jsx)("span",{className:"whitespace-nowrap uppercase text-white text-sm font-medium",children:e.text})}),(0,r.jsx)(c.A,{className:"w-4 h-4 text-white ml-1 hidden md:flex",size:"12"})]}),i(e.id,!0)]}):(0,r.jsx)(d(),{href:"/".concat(e.slug),className:"block h-full",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-full px-2 py-2 hover:bg-red-700",children:(0,r.jsx)("span",{className:"whitespace-nowrap uppercase text-white text-sm font-medium",children:e.text})})})},e.id))]}):null};return(0,r.jsx)("div",{id:"nav",className:"border-t border-red-800 bottom-nav ".concat(t?"fixed top-0 shadow-md z-50 w-full":""),style:{backgroundColor:"#e4393c"},children:(0,r.jsxs)("div",{className:"container flex flex-nowrap items-center justify-between mx-auto px-0 md:px-2 overflow-x-scroll overflow-y-hidden md:overflow-visible",style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:[(0,r.jsx)("nav",{className:"w-full md:flex md:w-auto md:order-0",children:i(0)}),(0,r.jsx)("span",{className:"md:block hidden",children:(0,r.jsx)(x,{})})]})})},f=()=>{let{menus:e}=(0,o.i)();if(!e||!Array.isArray(e))return null;let t=e.find(e=>"0"===e.position);if(!t||!t.obj)return null;let s=t.obj.reduce((e,t)=>(e[t.parent]=e[t.parent]||[],e[t.parent].push(t),e),{}),l=()=>{let e=document.getElementById("my-drawer");e&&(e.checked=!1)},n=e=>s[e]?(0,r.jsx)("ul",{children:s[e].map(e=>{let t=!!s[e.id];return(0,r.jsx)("li",{children:t?(0,r.jsxs)("details",{children:[(0,r.jsx)("summary",{children:e.text}),n(e.id)]}):(0,r.jsx)(d(),{href:"/".concat(e.slug),onClick:l,children:e.text})},e.id)})}):null;return(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("ul",{className:"menu bg-base-200 rounded-box w-56",children:n(0)})})};var p=s(23348),v=s(73859);let b=()=>{let{user:e}=(0,p.U)(),t=(0,l.usePathname)(),[s,i]=(0,n.useState)(!1);(0,n.useEffect)(()=>{document.body.style.position="relative",document.body.style.overflowY="auto";let e=()=>{i(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[t]);let d=t.startsWith("/dashboard");return(0,r.jsx)(r.Fragment,{children:!d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("header",{className:"w-full hidden md:block",style:{height:"130px"},children:(0,r.jsx)("div",{style:{width:"100%",height:"130px",position:"relative",backgroundImage:"-webkit-gradient(linear, 0% 100%, 0% 0%, color-stop(0.2, rgb(255, 255, 255)), color-stop(0.8, rgb(255, 251, 213)))"},children:(0,r.jsxs)("div",{className:"container mx-auto relative",style:{background:"url(/trongdong_1546498623413.png) no-repeat",height:"130px",display:"flex",alignItems:"center"},children:[(0,r.jsxs)("div",{className:"flex items-center ml-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("a",{href:"/",children:(0,r.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",style:{width:"90px",height:"97px"}})})}),(0,r.jsxs)("div",{className:"flex flex-col ml-3",children:[(0,r.jsx)("div",{className:"text-base md:text-lg text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,r.jsx)("div",{className:"text-red-600",style:{fontFamily:'"Roboto Condensed", sans-serif',fontSize:"44px",fontWeight:700,fontStyle:"normal",lineHeight:"48.4px",color:"#dc2626 !important",WebkitTextFillColor:"#dc2626 !important"},children:"T\xd2A \xc1N NH\xc2N D\xc2N TP.HỒ CH\xcd MINH"})]})]}),(0,r.jsx)("div",{className:"absolute right-0 top-0 h-full",children:(0,r.jsx)("img",{src:"/bg-header_1546498587110.png",alt:"H\xecnh ảnh T\xf2a \xe1n",style:{height:"130px"}})})]})})}),(0,r.jsxs)("nav",{id:"top-nav",className:"w-full z-20 border-b border-gray-200 dark:border-gray-600",children:[(0,r.jsxs)("div",{className:"container flex flex-wrap items-center justify-between mx-auto",children:[(0,r.jsx)("div",{className:"block md:hidden",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{children:(0,r.jsx)("a",{href:"/",children:(0,r.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",style:{width:"60px",height:"65px"}})})}),(0,r.jsxs)("div",{className:"flex flex-col ml-2",children:[(0,r.jsx)("div",{className:"text-xs text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,r.jsx)("div",{className:"text-sm font-bold text-red-600",style:{color:"#dc2626 !important",WebkitTextFillColor:"#dc2626 !important"},children:"T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH"})]})]})}),(0,r.jsx)("div",{className:"w-1/3 hidden md:block"}),(0,r.jsxs)("div",{className:"right-block flex menu-search items-center justify-end w-auto ml-auto",children:[e&&(0,r.jsx)("span",{className:"md:hidden block",children:(0,r.jsx)(v.A,{})}),(0,r.jsx)("div",{className:"flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse",children:(0,r.jsxs)("div",{className:"drawer ml-2 block md:hidden",children:[(0,r.jsx)("input",{id:"my-drawer",type:"checkbox",className:"drawer-toggle"}),(0,r.jsx)("div",{className:"drawer-content",children:(0,r.jsx)("label",{htmlFor:"my-drawer",className:"cursor-pointer"})}),(0,r.jsxs)("div",{className:"drawer-side z-50 overflow-y-auto",children:[(0,r.jsx)("label",{htmlFor:"my-drawer","aria-label":"close sidebar",className:"drawer-overlay"}),(0,r.jsxs)("div",{className:"menu bg-base-200 text-base-content min-h-full md:w-2/3 lg:w-1/2 w-full p-4",children:[(0,r.jsx)("label",{htmlFor:"my-drawer","aria-label":"close sidebar",className:"fixed right-2 top-2 w-8 h-8 bg-white rounded-full shadow-md pointer flex items-center justify-center",children:(0,r.jsx)(a.A,{})}),(0,r.jsx)(x,{}),(0,r.jsx)(f,{})]})]})]})})]})]}),(0,r.jsx)(u,{})]})]})})}},40685:(e,t,s)=>{Promise.resolve().then(s.bind(s,76083)),Promise.resolve().then(s.t.bind(s,98561,23)),Promise.resolve().then(s.t.bind(s,62301,23)),Promise.resolve().then(s.t.bind(s,44638,23)),Promise.resolve().then(s.bind(s,38543)),Promise.resolve().then(s.t.bind(s,85716,23)),Promise.resolve().then(s.bind(s,23348)),Promise.resolve().then(s.bind(s,45627)),Promise.resolve().then(s.bind(s,6821)),Promise.resolve().then(s.bind(s,36109)),Promise.resolve().then(s.bind(s,85137)),Promise.resolve().then(s.bind(s,99843)),Promise.resolve().then(s.t.bind(s,63999,23))},45627:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(12115),l=s(99843);let n=()=>{let{setting:e,loading:t}=(0,l.i)();return(0,r.useEffect)(()=>{if(!t&&(null==e?void 0:e.title)&&(document.title=e.title,e.desc)){let t=document.querySelector('meta[name="description"]');if(t)t.setAttribute("content",e.desc);else{let t=document.createElement("meta");t.name="description",t.content=e.desc,document.head.appendChild(t)}}},[e,t]),null}},63999:()=>{},85137:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(12115);let l=()=>((0,r.useEffect)(()=>{let e=()=>{if(document.getElementById("safe-marker-override"))return;let e=document.createElement("style");e.id="safe-marker-override",e.textContent="\n        /* Override user agent stylesheet for ::marker */\n        ::marker {\n          unicode-bidi: normal !important;\n          font-variant-numeric: normal !important;\n          text-transform: inherit !important;\n          text-indent: inherit !important;\n          text-align: inherit !important;\n          text-align-last: inherit !important;\n          content: none !important;\n          display: none !important;\n        }\n\n        /* Remove list markers completely */\n        ul, ol {\n          list-style: none !important;\n        }\n\n        ul::marker, ol::marker, li::marker {\n          content: none !important;\n          display: none !important;\n        }\n\n        /* Custom list styles if needed */\n        .custom-list {\n          list-style: disc !important;\n          padding-left: 1.5rem !important;\n        }\n\n        .custom-list-ordered {\n          list-style: decimal !important;\n          padding-left: 1.5rem !important;\n        }\n      ",document.head.appendChild(e)};e();let t=setInterval(e,5e3);return()=>{clearInterval(t);let e=document.getElementById("safe-marker-override");e&&e.remove()}},[]),null)}},e=>{e.O(0,[6873,4797,1974,9268,3235,8543,6874,6388,9179,2020,8441,5964,7358],()=>e(e.s=40685)),_N_E=e.O()}]);