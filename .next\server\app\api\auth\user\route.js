(()=>{var a={};a.id=3414,a.ids=[3414],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63938:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>z,patchFetch:()=>y,routeModule:()=>u,serverHooks:()=>x,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var d=c(96559),e=c(48088),f=c(37719),g=c(26191),h=c(81289),i=c(261),j=c(92603),k=c(39893),l=c(14823),m=c(47220),n=c(66946),o=c(47912),p=c(99786),q=c(46143),r=c(86439),s=c(43365),t=c(96138);let u=new d.AppRouteRouteModule({definition:{kind:e.RouteKind.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:x}=u;function y(){return(0,f.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}async function z(a,b,c){var d;let f="/api/auth/user/route";"/index"===f&&(f="/");let t=await u.prepare(a,b,{srcPage:f,multiZoneDraftMode:"false"});if(!t)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:x,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=t,E=(0,i.normalizeAppPath)(f),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new r.NoFallbackError}let G=null;!F||u.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===u.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,h.getTracer)(),L=K.getActiveScopeSpan(),M={params:w,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!x.experimental.dynamicIO,authInterrupts:!!x.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,g.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=x.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>u.onRequestError(a,b,d,A)},sharedContext:{buildId:v}},N=new j.NodeNextRequest(a),O=new j.NodeNextResponse(b),P=k.NextRequestAdapter.fromNodeNextRequest(N,(0,k.signalFromNodeResponse)(b));try{let d=async c=>u.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),i=async h=>{var i,j;let k=async({previousCacheEntry:e})=>{try{if(!(0,g.getRequestMeta)(a,"minimalMode")&&B&&C&&!e)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let f=await d(h);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,n.I)(N,O,f,M.renderOpts.pendingWaitUntil),null;{let a=await f.blob(),b=(0,o.toNodeOutgoingHttpHeaders)(f.headers);j&&(b[q.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=q.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=q.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:s.CachedRouteKind.APP_ROUTE,status:f.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==e?void 0:e.isStale)&&await u.onRequestError(a,b,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,m.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await u.handleResponse({req:a,nextConfig:x,cacheKey:G,routeKind:e.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==s.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,g.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let r=(0,o.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,g.getRequestMeta)(a,"minimalMode")&&F||r.delete(q.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||r.get("Cache-Control")||r.set("Cache-Control",(0,p.getCacheControlHeader)(l.cacheControl)),await (0,n.I)(N,O,new Response(l.value.body,{headers:r,status:l.value.status||200})),null};L?await i(L):await K.withPropagatedContext(a.headers,()=>K.trace(l.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},i))}catch(b){if(L||b instanceof r.NoFallbackError||await u.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,m.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,n.I)(N,O,new Response(null,{status:500})),null}}},66946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(30898),e=c(42471),f=c(47912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96138:()=>{},96487:()=>{},96559:(a,b,c)=>{"use strict";a.exports=c(44870)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431],()=>b(b.s=63938));module.exports=c})();