"use strict";exports.id=6310,exports.ids=[6310],exports.modules={5336:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},29072:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(9113);let e={getFieldConfiguration:(a="CourtCase",b)=>d.Ay.get(`/api/field-configurations?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldConfiguration:(a,b)=>d.Ay.put("/api/field-configurations",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldOrder:(a,b)=>d.Ay.put("/api/field-configurations/order",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldVisibility:(a,b)=>d.Ay.put("/api/field-configurations/visibility",a,{headers:{Authorization:`Bearer ${b}`}})}},35071:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},36101:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(9113);let e={getCustomFields:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),createCustomField:(a,b)=>d.Ay.post("/api/custom-fields",a,{headers:{Authorization:`Bearer ${b}`}}),updateCustomField:(a,b,c)=>d.Ay.put(`/api/custom-fields/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteCustomField:(a,b)=>d.Ay.delete(`/api/custom-fields/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldsOrder:(a,b)=>d.Ay.put("/api/custom-fields/order",{fieldOrders:a},{headers:{Authorization:`Bearer ${b}`}}),getCustomFieldStats:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}})}},43649:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44451:(a,b,c)=>{c.d(b,{A:()=>e});var d=c(9113);let e={getDateCountdowns:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),upsertDateCountdown:(a,b)=>d.Ay.post("/api/date-countdowns",a,{headers:{Authorization:`Bearer ${b}`}}),deleteDateCountdown:(a,b)=>d.Ay.delete(`/api/date-countdowns/${a}`,{headers:{Authorization:`Bearer ${b}`}}),getCountdownStats:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),getUpcomingDeadlines:(a="CourtCase",b=30,c)=>d.Ay.get(`/api/date-countdowns/upcoming?targetModel=${a}&days=${b}`,{headers:{Authorization:`Bearer ${c}`}})}},48730:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49269:(a,b,c)=>{c.d(b,{A:()=>j});var d=c(60687),e=c(43210),f=c(35071),g=c(43649),h=c(5336),i=c(48730);function j({date:a,warningDays:b=30,dangerDays:c=7,showIcon:j=!0,showDetailed:k=!1,size:l="md",className:m=""}){let[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)("none"),r=()=>{if(!n)return"Kh\xf4ng c\xf3 dữ liệu";if(n.isExpired)return"Đ\xe3 hết hạn";let b=new Date(a),c=new Date,d=new Date(c.getTime()+6e4*c.getTimezoneOffset()+252e5);b.setHours(0,0,0,0),d.setHours(0,0,0,0);let e=b.getTime()-d.getTime();if(e>0){let a=Math.ceil(e/864e5);return`Bắt đầu sau ${a} ng\xe0y`}if(k&&n.totalDays<=7)if(n.days>0)return`${n.days}d ${n.hours}h ${n.minutes}m`;else if(n.hours>0)return`${n.hours}h ${n.minutes}m ${n.seconds}s`;else return`${n.minutes}m ${n.seconds}s`;return 1===n.totalDays?"C\xf2n 1 ng\xe0y":n.totalDays>1?`C\xf2n ${n.totalDays} ng\xe0y`:"H\xf4m nay"};if(!a||!n)return null;let s={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:f.A,color:"text-red-600"},danger:{bg:"bg-red-50",text:"text-red-700",border:"border-red-300",icon:g.A,color:"text-red-500"},warning:{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-300",icon:g.A,color:"text-yellow-500"},safe:{bg:"bg-green-50",text:"text-green-700",border:"border-green-300",icon:h.A,color:"text-green-500"},none:{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-300",icon:i.A,color:"text-gray-500"}}[p],t={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-3 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-4 py-2 text-base",icon:16,gap:"gap-2"}}[l],u=s.icon;return(0,d.jsxs)("span",{className:`
        inline-flex items-center ${t.gap} ${t.container}
        ${s.bg} ${s.text} ${s.border}
        border rounded-full font-medium
        ${m}
      `,title:`${r()} - Hạn: ${new Date(a).toLocaleDateString("vi-VN",{timeZone:"Asia/Ho_Chi_Minh",year:"numeric",month:"2-digit",day:"2-digit"})} (GMT+7)`,children:[j&&(0,d.jsx)(u,{size:t.icon,className:s.color}),r()]})}}};