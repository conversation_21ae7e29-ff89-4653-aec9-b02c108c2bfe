(()=>{var a={};a.id=5740,a.ids=[5740],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10566:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\departments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16500:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={createDepartment:(a,b)=>d.Ay.post("/api/departments",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartments:(a,b)=>d.Ay.post("/api/departments/list",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartmentById:(a,b)=>d.Ay.get(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateDepartment:(a,b,c)=>d.Ay.put(`/api/departments/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteDepartment:(a,b)=>d.Ay.delete(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),addMemberToDepartment:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members`,b,{headers:{Authorization:`Bearer ${c}`}}),getDepartmentMembers:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members/list`,b,{headers:{Authorization:`Bearer ${c}`}}),updateMemberPermissions:(a,b,c,e)=>d.Ay.put(`/api/departments/${a}/members/${b}`,c,{headers:{Authorization:`Bearer ${e}`}}),removeMemberFromDepartment:(a,b,c)=>d.Ay.delete(`/api/departments/${a}/members/${b}`,{headers:{Authorization:`Bearer ${c}`}}),getAvailablePermissions:a=>d.Ay.get("/api/departments/permissions",{headers:{Authorization:`Bearer ${a}`}})}},18835:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),e().createElement("circle",{cx:"8.5",cy:"7",r:"4"}),e().createElement("line",{x1:"20",y1:"8",x2:"20",y2:"14"}),e().createElement("line",{x1:"23",y1:"11",x2:"17",y2:"11"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="UserPlus";let j=i},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22960:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),e().createElement("circle",{cx:"12",cy:"12",r:"3"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Eye";let j=i},25524:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("polyline",{points:"3 6 5 6 21 6"}),e().createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),e().createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),e().createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Trash2";let j=i},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37360:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["departments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,10566)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/departments/page",pathname:"/dashboard/departments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/departments/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39953:(a,b,c)=>{Promise.resolve().then(c.bind(c,71756))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43956:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),e().createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),e().createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Save";let j=i},45073:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(60687);function e({currentPage:a,totalPages:b,onPageChange:c}){return(0,d.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,d.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>c(Math.max(a-1,1)),disabled:1===a,children:"Previous"}),(0,d.jsxs)("span",{children:["Page ",a," / ",b]}),(0,d.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>c(Math.min(a+1,b)),disabled:a===b,children:"Next"})]})}c(43210)},46345:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),e().createElement("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Edit";let j=i},49681:(a,b,c)=>{Promise.resolve().then(c.bind(c,10566))},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69545:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),e().createElement("line",{x1:"5",y1:"12",x2:"19",y2:"12"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Plus";let j=i},71170:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(60687);c(43210);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},f=c[a]||c.user;return(0,d.jsx)(e,{variant:f.variant,className:b,children:f.label})}},71756:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>I});var d=c(60687),e=c(43210),f=c(56090),g=c(93772),h=c(16500),i=c(93853),j=c(16189),k=c(45073),l=c(71170),m=c(76957),n=c(46345),o=c(25524),p=c(69545),q=c(55109);function r(){let[a,b]=(0,e.useState)([]),[c,r]=(0,e.useState)(!0),[s,t]=(0,e.useState)(0),[u,v]=(0,e.useState)(1),[w,x]=(0,e.useState)(""),y=(0,j.useRouter)(),{isAdmin:z}=(0,q.S)(),A=[{accessorKey:"name",header:"T\xean ph\xf2ng ban",cell:({row:a})=>(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.original.name}),a.original.description&&(0,d.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:a.original.description})]})},{accessorKey:"manager",header:"Quản l\xfd",cell:({row:a})=>(0,d.jsx)("div",{children:a.original.manager?(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.original.manager.username}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.original.manager.email})]}):(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 quản l\xfd"})})},{accessorKey:"memberCount",header:"Số th\xe0nh vi\xean",cell:({row:a})=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{size:16,className:"text-gray-500"}),(0,d.jsx)("span",{className:"font-medium",children:a.original.memberCount})]})},{accessorKey:"defaultPermissions",header:"Quyền mặc định",cell:({row:a})=>(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.original.defaultPermissions.length>0?(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)(l.Ex,{variant:"secondary",className:"text-xs",children:[a.original.defaultPermissions.length," quyền"]})}):(0,d.jsx)("span",{className:"text-gray-400 italic text-sm",children:"Kh\xf4ng c\xf3 quyền"})})},{accessorKey:"isActive",header:"Trạng th\xe1i",cell:({row:a})=>(0,d.jsx)(l.Ex,{variant:a.original.isActive?"success":"danger",children:a.original.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})},{id:"actions",header:"Thao t\xe1c",cell:({row:a})=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>y.push(`/dashboard/departments/${a.original._id}`),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,d.jsx)(m.A,{size:16})}),z&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:()=>y.push(`/dashboard/departments/${a.original._id}/edit`),className:"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(n.A,{size:16})}),(0,d.jsx)("button",{onClick:()=>D(a.original._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(o.A,{size:16})})]})]})}],B=(0,f.N4)({data:a,columns:A,getCoreRowModel:(0,g.HT)()}),C=async(a,c="")=>{try{r(!0);let d=localStorage.getItem("sessionToken")||"",e=await h.A.getDepartments({page:a,perPage:10,search:c},d);e.payload.success?(b(e.payload.departments),t(e.payload.total)):i.oR.error("Kh\xf4ng thể tải danh s\xe1ch ph\xf2ng ban")}catch(a){console.error("Error fetching departments:",a),i.oR.error("Lỗi khi tải danh s\xe1ch ph\xf2ng ban")}finally{r(!1)}},D=async b=>{let c=a.find(a=>a._id===b),d=c?.memberCount||0,e="Bạn c\xf3 chắc chắn muốn x\xf3a ph\xf2ng ban n\xe0y?";if(d>0&&(e=`⚠️ CẢNH B\xc1O: Ph\xf2ng ban n\xe0y c\xf3 ${d} th\xe0nh vi\xean!

Khi x\xf3a ph\xf2ng ban:
• Tất cả ${d} th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban
• Họ sẽ trở th\xe0nh người d\xf9ng thường (kh\xf4ng thuộc ph\xf2ng ban n\xe0o)
• C\xe1c quyền li\xean quan đến ph\xf2ng ban sẽ bị mất

Bạn c\xf3 chắc chắn muốn tiếp tục?`),confirm(e))try{let a=localStorage.getItem("sessionToken")||"",c=await h.A.deleteDepartment(b,a);c.payload.success?(c.payload.affectedMembers>0?i.oR.success(`X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng! ${c.payload.affectedMembers} th\xe0nh vi\xean đ\xe3 được chuyển về trạng th\xe1i kh\xf4ng thuộc ph\xf2ng ban.`,{autoClose:5e3}):i.oR.success("X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng"),C(u,w)):i.oR.error(c.payload.message||"Kh\xf4ng thể x\xf3a ph\xf2ng ban")}catch(a){console.error("Error deleting department:",a),i.oR.error("Lỗi khi x\xf3a ph\xf2ng ban")}};return c?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tổng cộng ",s," ph\xf2ng ban"]})]}),z&&(0,d.jsxs)("button",{onClick:()=>y.push("/dashboard/departments/add"),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(p.A,{size:16}),"Th\xeam ph\xf2ng ban"]})]}),(0,d.jsxs)("form",{onSubmit:a=>{a.preventDefault(),v(1),C(1,w)},className:"flex gap-2",children:[(0,d.jsx)("input",{type:"text",placeholder:"T\xecm kiếm theo t\xean, m\xe3 hoặc m\xf4 tả...",value:w,onChange:a=>x(a.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"T\xecm kiếm"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:B.getHeaderGroups().map(a=>(0,d.jsx)("tr",{children:a.headers.map(a=>(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:a.isPlaceholder?null:(0,f.Kv)(a.column.columnDef.header,a.getContext())},a.id))},a.id))}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:B.getRowModel().rows.map(a=>(0,d.jsx)("tr",{className:"hover:bg-gray-50",children:a.getVisibleCells().map(a=>(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,f.Kv)(a.column.columnDef.cell,a.getContext())},a.id))},a.id))})]}),0===a.length&&(0,d.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Kh\xf4ng c\xf3 ph\xf2ng ban n\xe0o"})]}),s>10&&(0,d.jsx)(k.A,{currentPage:u,totalPages:Math.ceil(s/10),onPageChange:v})]})}var s=c(78314),t=c(18835),u=c(22960),v=c(5241),w=c(15317),x=c(88577);function y({isOpen:a,onClose:b,departmentId:c,onMemberAdded:f}){let[g,j]=(0,e.useState)([]),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)(!1),s=async()=>{try{l(!0);let a=localStorage.getItem("sessionToken")||"",b=await x.A.getAllUsers({page:1,perPage:100,search:m},a);if(b.payload.success){let a=b.payload.users.filter(a=>!a.department&&"admin"!==a.rule);j(a)}}catch(a){console.error("Error fetching users:",a),i.oR.error("Lỗi khi tải danh s\xe1ch người d\xf9ng")}finally{l(!1)}},u=async()=>{if(!o)return void i.oR.error("Vui l\xf2ng chọn người d\xf9ng");try{r(!0);let a=localStorage.getItem("sessionToken")||"",d=await h.A.addMemberToDepartment(c,{userId:o,departmentRole:"member",permissions:[]},a);d.payload.success?(i.oR.success("Đ\xe3 th\xeam th\xe0nh vi\xean th\xe0nh c\xf4ng"),f(),b(),p("")):i.oR.error(d.payload.message||"Kh\xf4ng thể th\xeam th\xe0nh vi\xean")}catch(a){console.error("Error adding member:",a),i.oR.error("Lỗi khi th\xeam th\xe0nh vi\xean")}finally{r(!1)}};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 text-gray-900",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Th\xeam th\xe0nh vi\xean mới"}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(v.A,{size:20})})]}),(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)("input",{type:"text",placeholder:"T\xecm kiếm người d\xf9ng...",value:m,onChange:a=>n(a.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"}),(0,d.jsx)(w.A,{size:16,className:"absolute left-3 top-3 text-gray-400"})]}),(0,d.jsx)("button",{onClick:()=>{s()},className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200",children:"T\xecm"})]}),(0,d.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:k?(0,d.jsx)("div",{className:"text-center py-4",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"})}):g.length>0?g.map(a=>(0,d.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer bg-white",children:[(0,d.jsx)("input",{type:"radio",name:"selectedUser",value:a._id,checked:o===a._id,onChange:a=>p(a.target.value),className:"mr-3 text-gray-900"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:a.username}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.email})]})]},a._id)):(0,d.jsx)("div",{className:"text-center py-4 text-gray-600",children:"Kh\xf4ng c\xf3 người d\xf9ng khả dụng"})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,d.jsxs)("button",{onClick:u,disabled:!o||q,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[q?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,d.jsx)(t.A,{size:16}),q?"Đang th\xeam...":"Th\xeam th\xe0nh vi\xean"]})]})]})}):null}var z=c(85444);let A={user_view:"Xem người d\xf9ng",user_add:"Th\xeam người d\xf9ng",user_edit:"Sửa người d\xf9ng",user_delete:"X\xf3a người d\xf9ng",user_import:"Nhập người d\xf9ng",user_export:"Xuất người d\xf9ng",file_view:"Xem file",file_upload:"Tải l\xean file",file_delete:"X\xf3a file",file_download:"Tải xuống file",court_case_view:"Xem vụ việc",court_case_create:"Tạo vụ việc",court_case_edit:"Sửa vụ việc",court_case_delete:"X\xf3a vụ việc",court_case_export:"Xuất vụ việc",court_case_import:"Nhập vụ việc",court_case_stats:"Thống k\xea vụ việc",department_view:"Xem ph\xf2ng ban",department_create:"Tạo ph\xf2ng ban",department_edit:"Sửa ph\xf2ng ban",department_delete:"X\xf3a ph\xf2ng ban",department_member_manage:"Quản l\xfd th\xe0nh vi\xean",system_settings_view:"Xem c\xe0i đặt hệ thống",system_settings_edit:"Sửa c\xe0i đặt hệ thống",system_logs_view:"Xem nhật k\xfd hệ thống",system_admin_full_access:"To\xe0n quyền quản trị",system_departments_manage:"Quản l\xfd ph\xf2ng ban hệ thống",system_users_manage:"Quản l\xfd người d\xf9ng hệ thống",system_settings_manage:"Quản l\xfd c\xe0i đặt hệ thống",analytics_view:"Xem thống k\xea",analytics_export:"Xuất thống k\xea",permissions_manage:"Quản l\xfd quyền",admin:"Quản trị vi\xean"},B=a=>A[a]||a;function C({isOpen:a,onClose:b,departmentId:c,departmentPermissions:f,onUserCreated:g}){let{user:j}=(0,s.U)(),[k,l]=(0,e.useState)({username:"",email:"",password:"",phonenumber:"",departmentRole:"member"}),[m,n]=(0,e.useState)([]),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!0),y=a=>{let{name:b,value:c}=a.target;l(a=>({...a,[b]:c}))},A=async()=>{if(!k.username.trim())return void i.oR.error("Vui l\xf2ng nhập t\xean người d\xf9ng");if(!k.email.trim())return void i.oR.error("Vui l\xf2ng nhập email");if(!w&&!k.password.trim())return void i.oR.error("Vui l\xf2ng nhập mật khẩu");try{r(!0);let a=localStorage.getItem("sessionToken")||"",d=w?(()=>{let a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",b="";for(let c=0;c<12;c++)b+=a.charAt(Math.floor(Math.random()*a.length));return b})():k.password,e=await h.A.addMemberToDepartment(c,{username:k.username,email:k.email,password:d,phonenumber:k.phonenumber,departmentRole:k.departmentRole,permissions:m},a);e.payload.success?(i.oR.success(`Đ\xe3 tạo t\xe0i khoản th\xe0nh c\xf4ng! ${w?`Mật khẩu: ${d}`:""}`),g(),b()):i.oR.error(e.payload.message||"Kh\xf4ng thể tạo t\xe0i khoản")}catch(a){console.error("Error creating user:",a),i.oR.error("Lỗi khi tạo t\xe0i khoản")}finally{r(!1)}};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto text-gray-900",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo t\xe0i khoản mới"}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(v.A,{size:20})})]}),(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Th\xf4ng tin cơ bản"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xean người d\xf9ng *"}),(0,d.jsx)("input",{type:"text",name:"username",value:k.username,onChange:y,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập t\xean người d\xf9ng"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,d.jsx)("input",{type:"email",name:"email",value:k.email,onChange:y,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập email"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Số điện thoại"}),(0,d.jsx)("input",{type:"tel",name:"phonenumber",value:k.phonenumber,onChange:y,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập số điện thoại"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,d.jsxs)("select",{name:"departmentRole",value:k.departmentRole,onChange:y,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",children:[(0,d.jsx)("option",{value:"member",children:"Th\xe0nh vi\xean"}),(0,d.jsx)("option",{value:"deputy",children:"Ph\xf3 ph\xf2ng"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Mật khẩu"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:w,onChange:a=>x(a.target.checked),className:"mr-2 text-gray-900"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:"Tự động tạo mật khẩu"})]}),!w&&(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:o?"text":"password",name:"password",value:k.password,onChange:y,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Nhập mật khẩu"}),(0,d.jsx)("button",{type:"button",onClick:()=>p(!o),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:o?(0,d.jsx)(z.A,{size:16}):(0,d.jsx)(u.A,{size:16})})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Quyền truy cập"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Chọn c\xe1c quyền từ danh s\xe1ch quyền của ph\xf2ng ban"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-white",children:f.map(a=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:m.includes(a),onChange:b=>{b.target.checked?n(b=>[...b,a]):n(b=>b.filter(b=>b!==a))},className:"mr-2 text-gray-900"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:B(a)})]},a))})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,d.jsxs)("button",{onClick:A,disabled:q,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[q?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,d.jsx)(t.A,{size:16}),q?"Đang tạo...":"Tạo t\xe0i khoản"]})]})]})}):null}var D=c(80508),E=c(43956);function F({isOpen:a,onClose:b,member:c,departmentId:f,departmentPermissions:g,onPermissionsUpdated:j}){let[k,l]=(0,e.useState)([]),[m,n]=(0,e.useState)("member"),[o,p]=(0,e.useState)(!1),q=async()=>{if(c)try{p(!0);let a=localStorage.getItem("sessionToken")||"",d=await h.A.updateMemberPermissions(f,c._id,{permissions:k,departmentRole:m},a);d.payload.success?(i.oR.success("Đ\xe3 cập nhật quyền th\xe0nh c\xf4ng"),j(),b()):i.oR.error(d.payload.message||"Kh\xf4ng thể cập nhật quyền")}catch(a){console.error("Error updating permissions:",a),i.oR.error("Lỗi khi cập nhật quyền")}finally{p(!1)}};return a&&c?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto text-gray-900",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(D.A,{className:"text-blue-600",size:20}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa quyền th\xe0nh vi\xean"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[c.username," (",c.email,")"]})]})]}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(v.A,{size:20})})]}),(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,d.jsxs)("select",{value:m,onChange:a=>n(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",children:[(0,d.jsx)("option",{value:"member",children:"Th\xe0nh vi\xean"}),(0,d.jsx)("option",{value:"deputy",children:"Ph\xf3 ph\xf2ng"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:"Quyền truy cập"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>l(g),className:"text-sm text-blue-600 hover:text-blue-700",children:"Chọn tất cả"}),(0,d.jsx)("span",{className:"text-gray-300",children:"|"}),(0,d.jsx)("button",{onClick:()=>l([]),className:"text-sm text-red-600 hover:text-red-700",children:"Bỏ chọn tất cả"})]})]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Chọn c\xe1c quyền từ danh s\xe1ch quyền của ph\xf2ng ban (",g.length," quyền c\xf3 sẵn)"]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-white",children:g.map(a=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:k.includes(a),onChange:b=>{b.target.checked?l(b=>[...b,a]):l(b=>b.filter(b=>b!==a))},className:"mr-2 text-gray-900"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:B(a)})]},a))}),0===g.length&&(0,d.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Ph\xf2ng ban chưa c\xf3 quyền n\xe0o được cấu h\xecnh"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900",children:"T\xf3m tắt thay đổi"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"font-medium text-gray-900 mb-2",children:["Quyền hiện tại (",c.permissions?.length||0,"):"]}),(0,d.jsx)("div",{className:"space-y-1",children:c.permissions?.map(a=>(0,d.jsxs)("div",{className:"text-gray-700",children:["• ",B(a)]},a))||(0,d.jsx)("div",{className:"text-gray-600 italic",children:"Chưa c\xf3 quyền n\xe0o"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"font-medium text-gray-900 mb-2",children:["Quyền mới (",k.length,"):"]}),(0,d.jsxs)("div",{className:"space-y-1",children:[k.map(a=>(0,d.jsxs)("div",{className:"text-blue-700",children:["• ",B(a)]},a)),0===k.length&&(0,d.jsx)("div",{className:"text-gray-600 italic",children:"Kh\xf4ng c\xf3 quyền n\xe0o"})]})]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200",children:"Hủy"}),(0,d.jsxs)("button",{onClick:q,disabled:o,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[o?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,d.jsx)(E.A,{size:16}),o?"Đang cập nhật...":"Cập nhật quyền"]})]})]})}):null}function G(){let{user:a}=(0,s.U)(),{isDepartmentManager:b}=(0,q.S)(),[c,f]=(0,e.useState)(null),[g,j]=(0,e.useState)([]),[k,o]=(0,e.useState)(!0),[r,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!1),[z,A]=(0,e.useState)(!1),[D,E]=(0,e.useState)(null),G=async()=>{if(!a?.department)return void o(!1);try{let b=localStorage.getItem("sessionToken")||"",c=await h.A.getDepartmentMembers(a.department,{page:1,perPage:50,search:""},b);c.payload.success?j(c.payload.members):i.oR.error("Kh\xf4ng thể tải danh s\xe1ch th\xe0nh vi\xean")}catch(a){console.error("Error fetching members:",a),i.oR.error("Lỗi khi tải danh s\xe1ch th\xe0nh vi\xean")}finally{o(!1)}};return b?k?(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):c?(0,d.jsxs)("div",{className:"space-y-6 text-gray-900",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Th\xf4ng tin ph\xf2ng ban"}),(0,d.jsx)(l.Ex,{variant:"success",children:"Quản l\xfd"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900",children:c.name}),c.description&&(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:c.description})]}),(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{size:16,className:"text-gray-400"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[g.length," th\xe0nh vi\xean"]})]})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch th\xe0nh vi\xean"}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>v(!0),className:"flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors",children:[(0,d.jsx)(t.A,{size:16}),"Th\xeam th\xe0nh vi\xean c\xf3 sẵn"]}),(0,d.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(p.A,{size:16}),"Tạo t\xe0i khoản mới"]})]})]})}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Th\xe0nh vi\xean"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vai tr\xf2"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quyền"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Thao t\xe1c"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:a.username}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.email})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)(l.eG,{role:a.departmentRole||"member"})}),(0,d.jsx)("td",{className:"px-6 py-4",children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.permissions?.slice(0,3).map(a=>(0,d.jsx)(l.Ex,{variant:"secondary",size:"sm",children:B(a)},a)),a.permissions&&a.permissions.length>3&&(0,d.jsxs)(l.Ex,{variant:"secondary",size:"sm",children:["+",a.permissions.length-3]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,d.jsx)(u.A,{size:16})}),(0,d.jsx)("button",{onClick:()=>{E(a),A(!0)},className:"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Chỉnh sửa quyền",children:(0,d.jsx)(n.A,{size:16})})]})})]},a._id))})]})}),0===g.length&&(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Chưa c\xf3 th\xe0nh vi\xean n\xe0o"})})]}),(0,d.jsx)(y,{isOpen:r,onClose:()=>v(!1),departmentId:a?.department||"",onMemberAdded:()=>{G(),v(!1)}}),(0,d.jsx)(C,{isOpen:w,onClose:()=>x(!1),departmentId:a?.department||"",departmentPermissions:c?.defaultPermissions||[],onUserCreated:()=>{G(),x(!1)}}),(0,d.jsx)(F,{isOpen:z,onClose:()=>{A(!1),E(null)},member:D,departmentId:a?.department||"",departmentPermissions:c?.defaultPermissions||[],onPermissionsUpdated:()=>{G(),A(!1),E(null)}})]}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy th\xf4ng tin ph\xf2ng ban"})}):null}var H=c(98462);function I(){return(0,d.jsx)(H.default,{requiredPermission:"admin",children:(0,d.jsx)("div",{className:"content",children:(0,d.jsx)(J,{})})})}function J(){let{isAdmin:a,isDepartmentManager:b}=(0,q.S)();return b?(0,d.jsx)(G,{}):a?(0,d.jsx)(r,{}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Bạn kh\xf4ng c\xf3 quyền truy cập trang n\xe0y"})})}},79428:a=>{"use strict";a.exports=require("buffer")},85444:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),e().createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="EyeOff";let j=i},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88577:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={fetchUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),getAllUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),fetchLogs:(a,b)=>d.Ay.get(`api/administrator/log/${a}`,{headers:{Authorization:`Bearer ${b}`}}),deleteUser:(a,b)=>d.Ay.delete(`api/administrator/users/${a._id}`,{headers:{Authorization:`Bearer ${b}`}}),fetchUserById:(a,b,c)=>d.Ay.get(`api/administrator/users/${a}`,{headers:{Authorization:`Bearer ${b}`},signal:c}),CreateUser:(a,b)=>d.Ay.post("api/administrator/signup",a,{headers:{Authorization:`Bearer ${b}`}}),updateUser:(a,b)=>d.Ay.put("api/administrator/change-info/",a,{headers:{Authorization:`Bearer ${b}`}}),updatePassUser:(a,b)=>d.Ay.put("api/administrator/users/change-pass/",a,{headers:{Authorization:`Bearer ${b}`}})}},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isDepartmentManager:l,isLoading:m}=(0,e.S)(),n=(0,f.useRouter)();if(m)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?"admin"===b&&!!l||i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>n.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,6090,9377,5600],()=>b(b.s=37360));module.exports=c})();