(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3871],{11080:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(12115),a=s(38637),n=s.n(a);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}var o=(0,r.forwardRef)(function(e,t){var s=e.color,a=e.size,n=void 0===a?24:a,o=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s,r,a={},n=Object.keys(e);for(r=0;r<n.length;r++)s=n[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)s=n[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,["color","size"]);return r.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:n,height:n,viewBox:"0 0 24 24",fill:"none",stroke:void 0===s?"currentColor":s,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),r.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),r.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),r.createElement("polyline",{points:"7 3 7 8 15 8"}))});o.propTypes={color:n().string,size:n().oneOfType([n().string,n().number])},o.displayName="Save";let l=o},11725:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(27937);let a={fetchUsers:(e,t)=>r.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>r.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>r.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>r.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,s)=>r.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:s}),CreateUser:(e,t)=>r.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>r.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>r.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},47829:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(95155),a=s(12115),n=s(35695),i=s(38543),o=s(3136),l=s(11725),c=s(87708),d=s(9424),h=s(11080);function u(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,[s,u]=(0,a.useState)(!1),[m,g]=(0,a.useState)(!0),[p,y]=(0,a.useState)([]),[b,x]=(0,a.useState)([]),[f,v]=(0,a.useState)({name:"",description:"",defaultPermissions:[],managerId:""}),[j,N]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t&&(k(),w(),A())},[t]);let k=async()=>{try{let r=localStorage.getItem("sessionToken")||"",a=await o.A.getDepartmentById(t,r);if(a.payload.success){var s;let e=a.payload.department;v({name:e.name,description:e.description||"",defaultPermissions:e.defaultPermissions,managerId:(null==(s=e.manager)?void 0:s._id)||""})}else i.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}catch(t){console.error("Error fetching department:",t),i.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}finally{g(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await o.A.getAvailablePermissions(e);t.payload.success&&y(t.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},A=async()=>{try{let e=localStorage.getItem("sessionToken")||"",s=await l.A.getAllUsers({page:1,perPage:100},e);if(s.payload.success){let e=s.payload.users.filter(e=>!e.department||"department_manager"!==e.rule||e.department._id===t);x(e)}}catch(e){console.error("Error fetching users:",e)}},C=async s=>{if(s.preventDefault(),!f.name.trim())return void i.oR.error("Vui l\xf2ng nhập t\xean ph\xf2ng ban");try{u(!0);let s=localStorage.getItem("sessionToken")||"",r=await o.A.updateDepartment(t,{...f,syncMemberPermissions:j},s);r.payload.success?(i.oR.success("Cập nhật ph\xf2ng ban th\xe0nh c\xf4ng"),e.push("/dashboard/departments/".concat(t))):i.oR.error(r.payload.message||"Kh\xf4ng thể cập nhật ph\xf2ng ban")}catch(e){console.error("Error updating department:",e),i.oR.error("Lỗi khi cập nhật ph\xf2ng ban")}finally{u(!1)}},P=p.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});return m?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsx)(c.default,{requiredPermission:"admin",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,r.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(d.A,{size:20})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa ph\xf2ng ban"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Cập nhật th\xf4ng tin v\xe0 quyền mặc định"})]})]}),(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin cơ bản"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean ph\xf2ng ban ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",value:f.name,onChange:e=>v(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean ph\xf2ng ban",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),(0,r.jsxs)("select",{value:f.managerId,onChange:e=>v(t=>({...t,managerId:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Chọn quản l\xfd ph\xf2ng ban"}),b.map(e=>(0,r.jsxs)("option",{value:e._id,children:[e.username," (",e.email,")"]},e._id))]})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,r.jsx)("textarea",{value:f.description,onChange:e=>v(t=>({...t,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về ph\xf2ng ban"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền mặc định cho người d\xf9ng"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c quyền n\xe0y sẽ được tự động g\xe1n cho tất cả người d\xf9ng mới của ph\xf2ng ban"}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(P).map(e=>{let[t,s]=e;return(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:t}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:s.map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:f.defaultPermissions.includes(e.key),onChange:t=>{var s,r;return s=e.key,r=t.target.checked,void v(e=>({...e,defaultPermissions:r?[...e.defaultPermissions,s]:e.defaultPermissions.filter(e=>e!==s)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},t)})}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"syncMemberPermissions",checked:j,onChange:e=>N(e.target.checked),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{htmlFor:"syncMemberPermissions",className:"text-sm font-medium text-gray-900 cursor-pointer",children:"Đồng bộ quyền cho tất cả th\xe0nh vi\xean hiện tại"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Khi được chọn, tất cả th\xe0nh vi\xean hiện tại trong ph\xf2ng ban sẽ được cập nhật quyền theo danh s\xe1ch quyền mặc định mới.",(0,r.jsx)("span",{className:"text-yellow-700 font-medium",children:" Cảnh b\xe1o: Điều n\xe0y sẽ ghi đ\xe8 l\xean tất cả quyền hiện tại của th\xe0nh vi\xean!"})]})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>e.back(),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,r.jsxs)("button",{type:"submit",disabled:s,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,r.jsx)(h.A,{size:16}),s?"Đang cập nhật...":"Cập nhật ph\xf2ng ban"]})]})]})]})})}},84646:(e,t,s)=>{Promise.resolve().then(s.bind(s,47829))}},e=>{e.O(0,[9268,3235,8543,3998,8441,5964,7358],()=>e(e.s=84646)),_N_E=e.O()}]);