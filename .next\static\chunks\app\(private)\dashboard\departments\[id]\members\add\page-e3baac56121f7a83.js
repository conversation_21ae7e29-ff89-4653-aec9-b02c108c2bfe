(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1234],{11080:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(12115),n=t(38637),a=t.n(n);function l(){return(l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}var o=(0,s.forwardRef)(function(e,r){var t=e.color,n=e.size,a=void 0===n?24:n,o=function(e,r){if(null==e)return{};var t,s,n=function(e,r){if(null==e)return{};var t,s,n={},a=Object.keys(e);for(s=0;s<a.length;s++)t=a[s],r.indexOf(t)>=0||(n[t]=e[t]);return n}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)t=a[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}(e,["color","size"]);return s.createElement("svg",l({ref:r,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),s.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),s.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),s.createElement("polyline",{points:"7 3 7 8 15 8"}))});o.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},o.displayName="Save";let i=o},18886:(e,r,t)=>{Promise.resolve().then(t.bind(t,39284))},21953:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(12115),n=t(38637),a=t.n(n);function l(){return(l=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}var o=(0,s.forwardRef)(function(e,r){var t=e.color,n=e.size,a=void 0===n?24:n,o=function(e,r){if(null==e)return{};var t,s,n=function(e,r){if(null==e)return{};var t,s,n={},a=Object.keys(e);for(s=0;s<a.length;s++)t=a[s],r.indexOf(t)>=0||(n[t]=e[t]);return n}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)t=a[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}(e,["color","size"]);return s.createElement("svg",l({ref:r,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),s.createElement("path",{d:"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.createElement("circle",{cx:"8.5",cy:"7",r:"4"}),s.createElement("line",{x1:"20",y1:"8",x2:"20",y2:"14"}),s.createElement("line",{x1:"23",y1:"11",x2:"17",y2:"11"}))});o.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},o.displayName="UserPlus";let i=o},39284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(95155),n=t(12115),a=t(35695),l=t(38543),o=t(3136),i=t(87708),c=t(9424),d=t(21953),h=t(11080);function m(){let e=(0,a.useRouter)(),r=(0,a.useParams)().id,[t,m]=(0,n.useState)(!1),[u,g]=(0,n.useState)(!0),[p,x]=(0,n.useState)([]),[y,b]=(0,n.useState)(null),[f,v]=(0,n.useState)({username:"",email:"",password:"",phonenumber:"",permissions:[]});(0,n.useEffect)(()=>{r&&(j(),N())},[r]);let j=async()=>{try{let t=localStorage.getItem("sessionToken")||"",s=await o.A.getDepartmentById(r,t);s.payload.success?(b(s.payload.department),v(e=>({...e,permissions:s.payload.department.defaultPermissions||[]}))):(l.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments"))}catch(r){console.error("Error fetching department:",r),l.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}finally{g(!1)}},N=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await o.A.getAvailablePermissions(e);r.payload.success&&x(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},w=async t=>{if(t.preventDefault(),!f.username.trim()||!f.email.trim())return void l.oR.error("Vui l\xf2ng điền đầy đủ th\xf4ng tin bắt buộc");try{m(!0);let t=localStorage.getItem("sessionToken")||"",s=await o.A.addMemberToDepartment(r,f,t);s.payload.success?(l.oR.success("Th\xeam th\xe0nh vi\xean v\xe0o ph\xf2ng ban th\xe0nh c\xf4ng"),s.payload.generatedPassword&&l.oR.info("Mật khẩu được tạo tự động: ".concat(s.payload.generatedPassword)),e.push("/dashboard/departments/".concat(r))):l.oR.error(s.payload.message||"Kh\xf4ng thể th\xeam th\xe0nh vi\xean")}catch(e){console.error("Error adding member:",e),l.oR.error("Lỗi khi th\xeam th\xe0nh vi\xean")}finally{m(!1)}},k=p.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return u?(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):y?(0,s.jsx)(i.default,{requiredPermission:"admin",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(c.A,{size:20})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Th\xeam th\xe0nh vi\xean mới"}),(0,s.jsxs)("p",{className:"text-gray-600 mt-1",children:["Th\xeam th\xe0nh vi\xean v\xe0o ph\xf2ng ban ",y.name]})]})]}),(0,s.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,s.jsx)(d.A,{size:20}),"Th\xf4ng tin cơ bản"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean người d\xf9ng ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:f.username,onChange:e=>v(r=>({...r,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean người d\xf9ng",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Email ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"email",value:f.email,onChange:e=>v(r=>({...r,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập địa chỉ email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mật khẩu"}),(0,s.jsx)("input",{type:"password",value:f.password,onChange:e=>v(r=>({...r,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Để trống để tạo tự động"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Nếu để trống, hệ thống sẽ tạo mật khẩu tự động"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),(0,s.jsx)("input",{type:"tel",value:f.phonenumber,onChange:e=>v(r=>({...r,phonenumber:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập số điện thoại"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền hạn"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Th\xe0nh vi\xean sẽ tự động nhận ",y.defaultPermissions.length," quyền mặc định của ph\xf2ng ban. Bạn c\xf3 thể th\xeam c\xe1c quyền bổ sung b\xean dưới."]}),(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(k).map(e=>{let[r,t]=e;return(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:r}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:t.map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:f.permissions.includes(e.key),onChange:r=>{var t,s;return t=e.key,s=r.target.checked,void v(e=>({...e,permissions:s?[...e.permissions,t]:e.permissions.filter(e=>e!==t)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},r)})}),(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-800",children:[(0,s.jsx)("strong",{children:"Tổng cộng:"})," Th\xe0nh vi\xean sẽ c\xf3 ",f.permissions.length," quyền được chọn."]})})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsxs)("button",{type:"submit",disabled:t,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,s.jsx)(h.A,{size:16}),t?"Đang th\xeam...":"Th\xeam th\xe0nh vi\xean"]})]})]})]})}):(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy ph\xf2ng ban"})})}}},e=>{e.O(0,[9268,3235,8543,3998,8441,5964,7358],()=>e(e.s=18886)),_N_E=e.O()}]);