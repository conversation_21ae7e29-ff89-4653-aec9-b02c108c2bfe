"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3602],{1243:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5116:(e,t,o)=>{o.d(t,{A:()=>a});var r=o(27937);let a={getFieldConfiguration:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return r.Ay.get("/api/field-configurations?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},updateFieldConfiguration:(e,t)=>r.Ay.put("/api/field-configurations",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldOrder:(e,t)=>r.Ay.put("/api/field-configurations/order",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldVisibility:(e,t)=>r.Ay.put("/api/field-configurations/visibility",e,{headers:{Authorization:"Bearer ".concat(t)}})}},9347:(e,t,o)=>{o.d(t,{A:()=>a});var r=o(27937);let a={getCustomFields:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return r.Ay.get("/api/custom-fields?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},createCustomField:(e,t)=>r.Ay.post("/api/custom-fields",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateCustomField:(e,t,o)=>r.Ay.put("/api/custom-fields/".concat(e),t,{headers:{Authorization:"Bearer ".concat(o)}}),deleteCustomField:(e,t)=>r.Ay.delete("/api/custom-fields/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldsOrder:(e,t)=>r.Ay.put("/api/custom-fields/order",{fieldOrders:e},{headers:{Authorization:"Bearer ".concat(t)}}),getCustomFieldStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return r.Ay.get("/api/custom-fields/stats?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})}}},14186:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},18594:(e,t,o)=>{o.d(t,{A:()=>l});var r=o(95155),a=o(12115),n=o(54861),i=o(1243),s=o(40646),c=o(14186);function l(e){let{date:t,warningDays:o=30,dangerDays:l=7,showIcon:d=!0,showDetailed:u=!1,size:h="md",className:g=""}=e,[p,y]=(0,a.useState)(null),[f,m]=(0,a.useState)("none");(0,a.useEffect)(()=>{if(!t){y(null),m("none");return}let e=()=>{let e=(()=>{let e=new Date(t),r=new Date(e);r.setDate(r.getDate()+o);let a=new Date,n=new Date(a.getTime()+6e4*a.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),r.setHours(0,0,0,0),n.setHours(0,0,0,0);let i=e.getTime()-n.getTime(),s=r.getTime()-n.getTime();if(s<0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(s/864e5),isExpired:!0};if(i>0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(s/864e5),isExpired:!1};let c=Math.floor(s/864e5),l=Math.floor(s%864e5/36e5);return{days:c,hours:l,minutes:Math.floor(s%36e5/6e4),seconds:Math.floor(s%6e4/1e3),totalDays:Math.ceil(s/864e5),isExpired:!1}})();if(y(e),e.isExpired)m("expired");else{let o=new Date(t),r=new Date,a=new Date(r.getTime()+6e4*r.getTimezoneOffset()+252e5);o.setHours(0,0,0,0),a.setHours(0,0,0,0),o.getTime()-a.getTime()>0?m("safe"):e.totalDays<=l?m("danger"):m("warning")}};e();let r=setInterval(e,1e3);return()=>clearInterval(r)},[t,o,l]);let A=()=>{if(!p)return"Kh\xf4ng c\xf3 dữ liệu";if(p.isExpired)return"Đ\xe3 hết hạn";let e=new Date(t),o=new Date,r=new Date(o.getTime()+6e4*o.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),r.setHours(0,0,0,0);let a=e.getTime()-r.getTime();if(a>0){let e=Math.ceil(a/864e5);return"Bắt đầu sau ".concat(e," ng\xe0y")}if(u&&p.totalDays<=7)if(p.days>0)return"".concat(p.days,"d ").concat(p.hours,"h ").concat(p.minutes,"m");else if(p.hours>0)return"".concat(p.hours,"h ").concat(p.minutes,"m ").concat(p.seconds,"s");else return"".concat(p.minutes,"m ").concat(p.seconds,"s");return 1===p.totalDays?"C\xf2n 1 ng\xe0y":p.totalDays>1?"C\xf2n ".concat(p.totalDays," ng\xe0y"):"H\xf4m nay"};if(!t||!p)return null;let v={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:n.A,color:"text-red-600"},danger:{bg:"bg-red-50",text:"text-red-700",border:"border-red-300",icon:i.A,color:"text-red-500"},warning:{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-300",icon:i.A,color:"text-yellow-500"},safe:{bg:"bg-green-50",text:"text-green-700",border:"border-green-300",icon:s.A,color:"text-green-500"},none:{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-300",icon:c.A,color:"text-gray-500"}}[f],w={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-3 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-4 py-2 text-base",icon:16,gap:"gap-2"}}[h],C=v.icon;return(0,r.jsxs)("span",{className:"\n        inline-flex items-center ".concat(w.gap," ").concat(w.container,"\n        ").concat(v.bg," ").concat(v.text," ").concat(v.border,"\n        border rounded-full font-medium\n        ").concat(g,"\n      "),title:"".concat(A()," - Hạn: ").concat(new Date(t).toLocaleDateString("vi-VN",{timeZone:"Asia/Ho_Chi_Minh",year:"numeric",month:"2-digit",day:"2-digit"})," (GMT+7)"),children:[d&&(0,r.jsx)(C,{size:w.icon,className:v.color}),A()]})}},19946:(e,t,o)=>{o.d(t,{A:()=>c});var r=o(12115);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,o)=>o?o.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return t.filter((e,t,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)((e,t)=>{let{color:o="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:l="",children:d,iconNode:u,...h}=e;return(0,r.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:o,strokeWidth:c?24*Number(s)/Number(a):s,className:n("lucide",l),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,o]=e;return(0,r.createElement)(t,o)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let o=(0,r.forwardRef)((o,i)=>{let{className:c,...l}=o;return(0,r.createElement)(s,{ref:i,iconNode:t,className:n("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...l})});return o.displayName=a(e),o}},27937:(e,t,o)=>{o.d(t,{Ay:()=>d});var r=o(84559),a=o(59434),n=o(35695);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class s extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let c=null,l=async(e,t,o)=>{let l;(null==o?void 0:o.body)instanceof FormData?l=o.body:(null==o?void 0:o.body)&&(l=JSON.stringify(o.body));let d=l instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==o?void 0:o.baseUrl)===void 0?r.A.NEXT_PUBLIC_API_ENDPOINT:o.baseUrl,h=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),g=await fetch(h,{...o,headers:{...d,...null==o?void 0:o.headers},body:l,method:e}),p=null,y=g.headers.get("content-type");if(y&&y.includes("application/json"))try{p=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await g.text();let f={status:g.status,payload:p};if(!g.ok)if(404===g.status||403===g.status)throw new s(f);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!c){c=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("https://toaantphcm.vn"))return};window.addEventListener("message",e),await c}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),c=null,location.href="/login"}}}else throw new i(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},d={get:(e,t)=>l("GET",e,t),post:(e,t,o)=>l("POST",e,{...o,body:t}),put:(e,t,o)=>l("PUT",e,{...o,body:t}),patch:(e,t,o)=>l("PATCH",e,{...o,body:t}),delete:(e,t)=>l("DELETE",e,{...t})}},40646:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54861:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},59434:(e,t,o)=>{o.d(t,{Fd:()=>i,cn:()=>n}),o(27937);var r=o(52596),a=o(39688);function n(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,a.QP)((0,r.$)(t))}o(58801);let i=e=>e.startsWith("/")?e.slice(1):e},84559:(e,t,o)=>{o.d(t,{A:()=>i});var r=o(74556),a=o(49509);let n=r.Ik({NEXT_PUBLIC_API_ENDPOINT:r.Yj().url(),NEXT_PUBLIC_URL:r.Yj().url(),CRYPTOJS_SECRECT:r.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"https://toaantphcm.vn",NEXT_PUBLIC_URL:"https://toaantphcm.vn",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=n.data},92493:(e,t,o)=>{o.d(t,{A:()=>a});var r=o(27937);let a={getDateCountdowns:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return r.Ay.get("/api/date-countdowns?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},upsertDateCountdown:(e,t)=>r.Ay.post("/api/date-countdowns",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteDateCountdown:(e,t)=>r.Ay.delete("/api/date-countdowns/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),getCountdownStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return r.Ay.get("/api/date-countdowns/stats?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},getUpcomingDeadlines:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,o=arguments.length>2?arguments[2]:void 0;return r.Ay.get("/api/date-countdowns/upcoming?targetModel=".concat(e,"&days=").concat(t),{headers:{Authorization:"Bearer ".concat(o)}})}}},92657:(e,t,o)=>{o.d(t,{A:()=>r});let r=(0,o(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);