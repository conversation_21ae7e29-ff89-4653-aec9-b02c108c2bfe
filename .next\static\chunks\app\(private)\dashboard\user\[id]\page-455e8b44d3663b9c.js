(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3664],{58870:(e,s,r)=>{Promise.resolve().then(r.bind(r,85520))},85520:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>v});var n=r(95155),i=r(12115),t=r(75937),l=r(62523),a=r(20174),o=r(62177),c=r(84649),d=r(63560),h=r(35695);let m=e=>{let{user:s,onSubmit:r,onSubmitPass:m}=e,[u,x]=(0,i.useState)(!1);(0,h.useRouter)();let[g,p]=(0,i.useState)(null),j=["Male","Female","Not"],v=["1","2","3","4","5"],b=["user","manager","editor"],f=(0,o.mN)({resolver:(0,d.u)(c.aP),defaultValues:s||{_id:"",email:"",username:"",phonenumber:"",private:!1,rule:"user",rank:"1",gender:"Not",bio:"",permissions:[]}}),y=f.watch("rule");i.useEffect(()=>{s&&(console.log("Resetting form with user data:",s),f.reset(s))},[s,f]),i.useEffect(()=>{console.log("Form state:",{isValid:f.formState.isValid,errors:f.formState.errors,values:f.getValues()}),Object.keys(f.formState.errors).length>0&&(console.log("Detailed validation errors:"),Object.entries(f.formState.errors).forEach(e=>{let[s,r]=e;console.log('Field "'.concat(s,'":'),r)}))},[f.formState.isValid,f.formState.errors]);let _=(0,o.mN)({resolver:(0,d.u)(c.gS),defaultValues:{_id:(null==s?void 0:s._id)||"",password:"",confirmPassword:""}});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t.lV,{...f,children:(0,n.jsxs)("form",{onSubmit:f.handleSubmit(e=>{var s;console.log("Form submitted with data:",e),console.log("Form errors:",f.formState.errors);let n={...e,phonenumber:(null==(s=e.phonenumber)?void 0:s.toString())||"",bio:e.bio||"",permissions:Array.isArray(e.permissions)?e.permissions:[]};console.log("Transformed data:",n),r(n)},e=>{console.log("Form validation errors:",e)}),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,n.jsx)(t.zB,{control:f.control,name:"username",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"User Name"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)(l.p,{placeholder:"username",...s})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"email",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Email"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)(l.p,{placeholder:"email",type:"email",...s})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"phonenumber",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Số điện thoại"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)(l.p,{placeholder:"Số điện thoại",type:"text",...s})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"gender",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Giới T\xednh"}),(0,n.jsx)(t.MJ,{children:(0,n.jsxs)("select",{...s,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,n.jsx)("option",{value:"",children:"Chọn giới t\xednh"}),j.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"rule",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Chức Vụ"}),(0,n.jsx)(t.MJ,{children:(0,n.jsxs)("select",{...s,className:"w-full p-2 border border-gray-300 rounded-md",disabled:"admin"===y,children:[(0,n.jsx)("option",{value:"",children:"Chọn chức vụ"}),b.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"rank",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Cấp độ th\xe0nh vi\xean"}),(0,n.jsx)(t.MJ,{children:(0,n.jsxs)("select",{...s,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,n.jsx)("option",{value:"",children:"Chọn cấp độ"}),v.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"bio",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Tiểu sử"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)("textarea",{...s,rows:3,className:"w-full p-2 border border-gray-300 rounded-md"})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"private",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Kho\xe1 Th\xe0nh vi\xean"}),(0,n.jsx)(t.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-col space-y-3 mt-2",children:[(0,n.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)("input",{type:"radio",name:"private-".concat(s.name),className:"w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3",value:"true",checked:!0===s.value,onChange:()=>s.onChange(!0)}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Kho\xe1 Kh\xe1ch h\xe0ng"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản sẽ bị v\xf4 hiệu h\xf3a"})]})]}),(0,n.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)("input",{type:"radio",name:"private-".concat(s.name),className:"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3",value:"false",checked:!1===s.value,onChange:()=>s.onChange(!1)}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Hoạt động"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản hoạt động b\xecnh thường"})]})]})]})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:f.control,name:"permissions",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{className:"col-span-2",children:[(0,n.jsx)(t.lR,{children:"Ph\xe2n quyền chức năng chi tiết"}),(0,n.jsx)("div",{className:"space-y-6 mt-4",children:[{title:"Th\xe0nh Vi\xean",permissions:[{id:"user_view",name:"Quản L\xfd Th\xe0nh Vi\xean",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin th\xe0nh vi\xean"},{id:"user_add",name:"Th\xeam Th\xe0nh Vi\xean",description:"Tạo t\xe0i khoản th\xe0nh vi\xean mới"},{id:"user_edit",name:"Chỉnh Sửa Th\xe0nh Vi\xean",description:"Cập nhật th\xf4ng tin th\xe0nh vi\xean"},{id:"user_delete",name:"X\xf3a Th\xe0nh Vi\xean",description:"X\xf3a t\xe0i khoản th\xe0nh vi\xean"},{id:"user_import_csv",name:"Nhập File CSV",description:"Import th\xe0nh vi\xean từ file CSV"}]},{title:"Quản L\xfd File",permissions:[{id:"file_view",name:"Xem File",description:"Xem danh s\xe1ch file v\xe0 t\xe0i liệu"},{id:"file_upload",name:"Upload File",description:"Tải l\xean file v\xe0 t\xe0i liệu mới"},{id:"file_delete",name:"X\xf3a File",description:"X\xf3a file v\xe0 t\xe0i liệu"}]},{title:"Quản L\xfd Vụ Việc T\xf2a \xc1n",permissions:[{id:"court_case_view",name:"Xem Danh S\xe1ch Vụ Việc",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_create",name:"Tạo Vụ Việc Mới",description:"Tạo vụ việc t\xf2a \xe1n mới"},{id:"court_case_edit",name:"Chỉnh Sửa Vụ Việc",description:"Chỉnh sửa th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_delete",name:"X\xf3a Vụ Việc",description:"X\xf3a vụ việc t\xf2a \xe1n"},{id:"court_case_export",name:"Xuất Dữ Liệu Vụ Việc",description:"Xuất danh s\xe1ch vụ việc ra file Excel/CSV"},{id:"court_case_import",name:"Nhập Dữ Liệu Vụ Việc",description:"Nhập danh s\xe1ch vụ việc từ file Excel"},{id:"court_case_stats_view",name:"Xem Thống K\xea Vụ Việc",description:"Xem thống k\xea cơ bản về vụ việc t\xf2a \xe1n"},{id:"court_case_detailed_stats_view",name:"Xem Thống K\xea Chi Tiết",description:"Xem thống k\xea chi tiết v\xe0 b\xe1o c\xe1o ph\xe2n t\xedch"}]},{title:"Quản L\xfd T\xe0i Khoản Trong Vụ Việc",permissions:[{id:"court_case_user_profile_view",name:"Xem Hồ Sơ Người D\xf9ng",description:"Xem th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"court_case_user_profile_edit",name:"Chỉnh Sửa Hồ Sơ",description:"Chỉnh sửa th\xf4ng tin hồ sơ người d\xf9ng"},{id:"court_case_user_password_change",name:"Đổi Mật Khẩu Người D\xf9ng",description:"Thay đổi mật khẩu cho người d\xf9ng"},{id:"court_case_user_permissions_view",name:"Xem Quyền Hạn",description:"Xem danh s\xe1ch quyền hạn của người d\xf9ng"},{id:"court_case_user_permissions_edit",name:"Chỉnh Sửa Quyền Hạn",description:"Cấp v\xe0 thu hồi quyền hạn cho người d\xf9ng"},{id:"court_case_user_activity_log_view",name:"Xem Nhật K\xfd Hoạt Động",description:"Xem lịch sử hoạt động của người d\xf9ng"},{id:"court_case_user_two_factor_manage",name:"Quản L\xfd X\xe1c Thực 2 Yếu Tố",description:"Bật/tắt v\xe0 quản l\xfd x\xe1c thực 2 yếu tố"}]},{title:"C\xe0i Đặt Hệ Thống",permissions:[{id:"system_settings_view",name:"Xem C\xe0i Đặt",description:"Xem cấu h\xecnh hệ thống"},{id:"system_settings_edit",name:"Chỉnh Sửa C\xe0i Đặt",description:"Thay đổi cấu h\xecnh hệ thống"}]},{title:"Thống K\xea & Ph\xe2n Quyền",permissions:[{id:"analytics_view",name:"Xem Thống K\xea",description:"Truy cập b\xe1o c\xe1o v\xe0 thống k\xea"},{id:"permissions_manage",name:"Quản L\xfd Ph\xe2n Quyền",description:"Cấp v\xe0 thu hồi quyền cho người d\xf9ng"}]}].map(e=>(0,n.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,n.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,n.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),e.title]}),(0,n.jsx)("div",{className:"space-y-2",children:e.permissions.map(e=>{var r;return(0,n.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors",children:[(0,n.jsx)("input",{type:"checkbox",className:"mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",value:e.id,checked:null==(r=s.value)?void 0:r.includes(e.id),onChange:r=>{var n;let i=r.target.checked?[...s.value||[],e.id]:(null==(n=s.value)?void 0:n.filter(s=>s!==e.id))||[];s.onChange(i)}}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,n.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id)})})]},e.title))}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400",children:[(0,n.jsx)("strong",{children:"Lưu \xfd:"})," Admin lu\xf4n c\xf3 tất cả quyền. Chỉ cần cấp quyền cụ thể cho Manager v\xe0 User."]})]})}})]}),(0,n.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:g}),(0,n.jsx)("div",{className:"flex gap-4 justify-center mt-6",children:(0,n.jsxs)("button",{disabled:!!u,type:"submit",onClick:()=>{console.log("Submit button clicked"),console.log("Loading state:",u),console.log("Form valid:",f.formState.isValid),console.log("Form errors:",f.formState.errors)},className:"btn btn-primary bg-blue-700 w-40 text-white flex items-center",children:[u?(0,n.jsx)(a.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})})]})}),(0,n.jsx)(t.lV,{..._,children:(0,n.jsxs)("form",{onSubmit:_.handleSubmit(m),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8",noValidate:!0,children:[(0,n.jsx)(t.zB,{control:_.control,name:"password",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"Mật khẩu mới"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)(l.p,{placeholder:"password",type:"password",...s})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsx)(t.zB,{control:_.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,n.jsxs)(t.eI,{children:[(0,n.jsx)(t.lR,{children:"X\xe1c nhận mật khẩu"}),(0,n.jsx)(t.MJ,{children:(0,n.jsx)(l.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...s})}),(0,n.jsx)(t.C5,{})]})}}),(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(a.A,{className:"animate-spin"}):"","Update Password"]})]})})]})};var u=r(11725),x=r(38543),g=r(6874),p=r.n(g),j=r(87708);function v(e){let{params:s}=e,[r,t]=(0,i.useState)(null),l=(0,i.use)(s).id;(0,i.useEffect)(()=>{let e=new AbortController,{signal:s}=e,r=async()=>{try{let r=localStorage.getItem("sessionToken")||"";console.log("Fetching user with ID:",l),console.log("Session token exists:",!!r);let n=await u.A.fetchUserById(l,r,s);if(!s.aborted)if(console.log("User fetch result:",n),n.payload.success)t(n.payload.user);else{var e;console.error("Error fetching user:",n.payload),x.oR.error("Failed to fetch user data: "+((null==(e=n.payload)?void 0:e.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){s.aborted||(console.error("Unexpected error:",e),x.oR.error("An error occurred while fetching user data"))}};return l&&r(),()=>{e.abort()}},[l]);let a=async e=>{try{console.log("Submitting user update data:",e);let r=localStorage.getItem("sessionToken")||"",n=await u.A.updateUser(e,r);if(console.log("Update result:",n),n.payload.success)t(n.payload.user),x.oR.success("Cập nhật th\xe0nh c\xf4ng!");else{var s;console.error("Error updating user:",n.payload),x.oR.error("Kh\xf4ng thể cập nhật: "+((null==(s=n.payload)?void 0:s.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),x.oR.error("C\xf3 lỗi xảy ra khi cập nhật. Vui l\xf2ng thử lại.")}},o=async e=>{try{console.log("Submitting password change data:",e);let r=localStorage.getItem("sessionToken")||"",n=await u.A.updatePassUser(e,r);if(console.log("Password change result:",n),n.payload.success)x.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng!");else{var s;console.error("Error changing password:",n.payload),x.oR.error("Kh\xf4ng thể đổi mật khẩu: "+((null==(s=n.payload)?void 0:s.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),x.oR.error("C\xf3 lỗi xảy ra khi đổi mật khẩu. Vui l\xf2ng thử lại.")}};return(0,n.jsx)(j.default,{requiredPermission:"user_edit",children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa t\xe0i khoản"}),(0,n.jsx)(p(),{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",href:"/dashboard/user/log/".concat(null==r?void 0:r._id),children:"Xem User log"})]}),r?(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,n.jsx)(m,{onSubmit:a,onSubmitPass:o,user:r})]})}):(0,n.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-gray-500",children:"Đang tải th\xf4ng tin..."})]})})]})})}}},e=>{e.O(0,[9268,3235,8543,2182,6874,9047,8441,5964,7358],()=>e(e.s=58870)),_N_E=e.O()}]);