(()=>{var a={};a.id=3664,a.ids=[3664],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1354:(a,b,c)=>{Promise.resolve().then(c.bind(c,66322))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31568:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["user",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,51830)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/user/[id]/page",pathname:"/dashboard/user/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/user/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},51830:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\user\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66322:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>t});var d=c(60687),e=c(43210),f=c.n(e),g=c(80942),h=c(89667),i=c(51714),j=c(27605),k=c(75707),l=c(558),m=c(16189);let n=({user:a,onSubmit:b,onSubmitPass:c})=>{let[n,o]=(0,e.useState)(!1);(0,m.useRouter)();let[p,q]=(0,e.useState)(null),r=["Male","Female","Not"],s=["1","2","3","4","5"],t=["user","manager","editor"],u=(0,j.mN)({resolver:(0,l.u)(k.aP),defaultValues:a||{_id:"",email:"",username:"",phonenumber:"",private:!1,rule:"user",rank:"1",gender:"Not",bio:"",permissions:[]}}),v=u.watch("rule");f().useEffect(()=>{a&&(console.log("Resetting form with user data:",a),u.reset(a))},[a,u]),f().useEffect(()=>{console.log("Form state:",{isValid:u.formState.isValid,errors:u.formState.errors,values:u.getValues()}),Object.keys(u.formState.errors).length>0&&(console.log("Detailed validation errors:"),Object.entries(u.formState.errors).forEach(([a,b])=>{console.log(`Field "${a}":`,b)}))},[u.formState.isValid,u.formState.errors]);let w=(0,j.mN)({resolver:(0,l.u)(k.gS),defaultValues:{_id:a?._id||"",password:"",confirmPassword:""}});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.lV,{...u,children:(0,d.jsxs)("form",{onSubmit:u.handleSubmit(a=>{console.log("Form submitted with data:",a),console.log("Form errors:",u.formState.errors);let c={...a,phonenumber:a.phonenumber?.toString()||"",bio:a.bio||"",permissions:Array.isArray(a.permissions)?a.permissions:[]};console.log("Transformed data:",c),b(c)},a=>{console.log("Form validation errors:",a)}),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,d.jsx)(g.zB,{control:u.control,name:"username",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"User Name"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"username",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"email",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Email"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"email",type:"email",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"phonenumber",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Số điện thoại"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Số điện thoại",type:"text",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"gender",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Giới T\xednh"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("select",{...a,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,d.jsx)("option",{value:"",children:"Chọn giới t\xednh"}),r.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"rule",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Chức Vụ"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("select",{...a,className:"w-full p-2 border border-gray-300 rounded-md",disabled:"admin"===v,children:[(0,d.jsx)("option",{value:"",children:"Chọn chức vụ"}),t.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"rank",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Cấp độ th\xe0nh vi\xean"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("select",{...a,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,d.jsx)("option",{value:"",children:"Chọn cấp độ"}),s.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"bio",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Tiểu sử"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,rows:3,className:"w-full p-2 border border-gray-300 rounded-md"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"private",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Kho\xe1 Th\xe0nh vi\xean"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("div",{className:"flex flex-col space-y-3 mt-2",children:[(0,d.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,d.jsx)("input",{type:"radio",name:`private-${a.name}`,className:"w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3",value:"true",checked:!0===a.value,onChange:()=>a.onChange(!0)}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Kho\xe1 Kh\xe1ch h\xe0ng"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản sẽ bị v\xf4 hiệu h\xf3a"})]})]}),(0,d.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,d.jsx)("input",{type:"radio",name:`private-${a.name}`,className:"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3",value:"false",checked:!1===a.value,onChange:()=>a.onChange(!1)}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Hoạt động"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản hoạt động b\xecnh thường"})]})]})]})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"permissions",render:({field:a})=>(0,d.jsxs)(g.eI,{className:"col-span-2",children:[(0,d.jsx)(g.lR,{children:"Ph\xe2n quyền chức năng chi tiết"}),(0,d.jsx)("div",{className:"space-y-6 mt-4",children:[{title:"Th\xe0nh Vi\xean",permissions:[{id:"user_view",name:"Quản L\xfd Th\xe0nh Vi\xean",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin th\xe0nh vi\xean"},{id:"user_add",name:"Th\xeam Th\xe0nh Vi\xean",description:"Tạo t\xe0i khoản th\xe0nh vi\xean mới"},{id:"user_edit",name:"Chỉnh Sửa Th\xe0nh Vi\xean",description:"Cập nhật th\xf4ng tin th\xe0nh vi\xean"},{id:"user_delete",name:"X\xf3a Th\xe0nh Vi\xean",description:"X\xf3a t\xe0i khoản th\xe0nh vi\xean"},{id:"user_import_csv",name:"Nhập File CSV",description:"Import th\xe0nh vi\xean từ file CSV"}]},{title:"Quản L\xfd File",permissions:[{id:"file_view",name:"Xem File",description:"Xem danh s\xe1ch file v\xe0 t\xe0i liệu"},{id:"file_upload",name:"Upload File",description:"Tải l\xean file v\xe0 t\xe0i liệu mới"},{id:"file_delete",name:"X\xf3a File",description:"X\xf3a file v\xe0 t\xe0i liệu"}]},{title:"Quản L\xfd Vụ Việc T\xf2a \xc1n",permissions:[{id:"court_case_view",name:"Xem Danh S\xe1ch Vụ Việc",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_create",name:"Tạo Vụ Việc Mới",description:"Tạo vụ việc t\xf2a \xe1n mới"},{id:"court_case_edit",name:"Chỉnh Sửa Vụ Việc",description:"Chỉnh sửa th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_delete",name:"X\xf3a Vụ Việc",description:"X\xf3a vụ việc t\xf2a \xe1n"},{id:"court_case_export",name:"Xuất Dữ Liệu Vụ Việc",description:"Xuất danh s\xe1ch vụ việc ra file Excel/CSV"},{id:"court_case_import",name:"Nhập Dữ Liệu Vụ Việc",description:"Nhập danh s\xe1ch vụ việc từ file Excel"},{id:"court_case_stats_view",name:"Xem Thống K\xea Vụ Việc",description:"Xem thống k\xea cơ bản về vụ việc t\xf2a \xe1n"},{id:"court_case_detailed_stats_view",name:"Xem Thống K\xea Chi Tiết",description:"Xem thống k\xea chi tiết v\xe0 b\xe1o c\xe1o ph\xe2n t\xedch"}]},{title:"Quản L\xfd T\xe0i Khoản Trong Vụ Việc",permissions:[{id:"court_case_user_profile_view",name:"Xem Hồ Sơ Người D\xf9ng",description:"Xem th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"court_case_user_profile_edit",name:"Chỉnh Sửa Hồ Sơ",description:"Chỉnh sửa th\xf4ng tin hồ sơ người d\xf9ng"},{id:"court_case_user_password_change",name:"Đổi Mật Khẩu Người D\xf9ng",description:"Thay đổi mật khẩu cho người d\xf9ng"},{id:"court_case_user_permissions_view",name:"Xem Quyền Hạn",description:"Xem danh s\xe1ch quyền hạn của người d\xf9ng"},{id:"court_case_user_permissions_edit",name:"Chỉnh Sửa Quyền Hạn",description:"Cấp v\xe0 thu hồi quyền hạn cho người d\xf9ng"},{id:"court_case_user_activity_log_view",name:"Xem Nhật K\xfd Hoạt Động",description:"Xem lịch sử hoạt động của người d\xf9ng"},{id:"court_case_user_two_factor_manage",name:"Quản L\xfd X\xe1c Thực 2 Yếu Tố",description:"Bật/tắt v\xe0 quản l\xfd x\xe1c thực 2 yếu tố"}]},{title:"C\xe0i Đặt Hệ Thống",permissions:[{id:"system_settings_view",name:"Xem C\xe0i Đặt",description:"Xem cấu h\xecnh hệ thống"},{id:"system_settings_edit",name:"Chỉnh Sửa C\xe0i Đặt",description:"Thay đổi cấu h\xecnh hệ thống"}]},{title:"Thống K\xea & Ph\xe2n Quyền",permissions:[{id:"analytics_view",name:"Xem Thống K\xea",description:"Truy cập b\xe1o c\xe1o v\xe0 thống k\xea"},{id:"permissions_manage",name:"Quản L\xfd Ph\xe2n Quyền",description:"Cấp v\xe0 thu hồi quyền cho người d\xf9ng"}]}].map(b=>(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,d.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),b.title]}),(0,d.jsx)("div",{className:"space-y-2",children:b.permissions.map(b=>(0,d.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors",children:[(0,d.jsx)("input",{type:"checkbox",className:"mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",value:b.id,checked:a.value?.includes(b.id),onChange:c=>{let d=c.target.checked?[...a.value||[],b.id]:a.value?.filter(a=>a!==b.id)||[];a.onChange(d)}}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:b.name}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:b.description})]})]},b.id))})]},b.title))}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400",children:[(0,d.jsx)("strong",{children:"Lưu \xfd:"})," Admin lu\xf4n c\xf3 tất cả quyền. Chỉ cần cấp quyền cụ thể cho Manager v\xe0 User."]})]})})]}),(0,d.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:p}),(0,d.jsx)("div",{className:"flex gap-4 justify-center mt-6",children:(0,d.jsxs)("button",{disabled:!!n,type:"submit",onClick:()=>{console.log("Submit button clicked"),console.log("Loading state:",n),console.log("Form valid:",u.formState.isValid),console.log("Form errors:",u.formState.errors)},className:"btn btn-primary bg-blue-700 w-40 text-white flex items-center",children:[n?(0,d.jsx)(i.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})})]})}),(0,d.jsx)(g.lV,{...w,children:(0,d.jsxs)("form",{onSubmit:w.handleSubmit(c),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8",noValidate:!0,children:[(0,d.jsx)(g.zB,{control:w.control,name:"password",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Mật khẩu mới"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"password",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:w.control,name:"confirmPassword",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"X\xe1c nhận mật khẩu"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("button",{disabled:!!n,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[n?(0,d.jsx)(i.A,{className:"animate-spin"}):"","Update Password"]})]})})]})};var o=c(88577),p=c(93853),q=c(85814),r=c.n(q),s=c(98462);function t({params:a}){let[b,c]=(0,e.useState)(null);(0,e.use)(a).id;let f=async a=>{try{console.log("Submitting user update data:",a);let b=localStorage.getItem("sessionToken")||"",d=await o.A.updateUser(a,b);console.log("Update result:",d),d.payload.success?(c(d.payload.user),p.oR.success("Cập nhật th\xe0nh c\xf4ng!")):(console.error("Error updating user:",d.payload),p.oR.error("Kh\xf4ng thể cập nhật: "+(d.payload?.message||"Lỗi kh\xf4ng x\xe1c định")))}catch(a){console.error("Unexpected error:",a),p.oR.error("C\xf3 lỗi xảy ra khi cập nhật. Vui l\xf2ng thử lại.")}},g=async a=>{try{console.log("Submitting password change data:",a);let b=localStorage.getItem("sessionToken")||"",c=await o.A.updatePassUser(a,b);console.log("Password change result:",c),c.payload.success?p.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng!"):(console.error("Error changing password:",c.payload),p.oR.error("Kh\xf4ng thể đổi mật khẩu: "+(c.payload?.message||"Lỗi kh\xf4ng x\xe1c định")))}catch(a){console.error("Unexpected error:",a),p.oR.error("C\xf3 lỗi xảy ra khi đổi mật khẩu. Vui l\xf2ng thử lại.")}};return(0,d.jsx)(s.default,{requiredPermission:"user_edit",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa t\xe0i khoản"}),(0,d.jsx)(r(),{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",href:`/dashboard/user/log/${b?._id}`,children:"Xem User log"})]}),b?(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,d.jsx)(n,{onSubmit:f,onSubmitPass:g,user:b})]})}):(0,d.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-500",children:"Đang tải th\xf4ng tin..."})]})})]})})}},66850:(a,b,c)=>{Promise.resolve().then(c.bind(c,51830))},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,2415,9377,5600,9018],()=>b(b.s=31568));module.exports=c})();