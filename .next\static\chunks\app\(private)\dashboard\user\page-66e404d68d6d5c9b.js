(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4778],{18617:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var a=t(27937);let s={getDashboardStats:e=>a.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}}),getRecentActivities:(e,r)=>a.Ay.get("/api/administrator/recent-activities".concat(r?"?limit=".concat(r):""),{headers:{Authorization:"Bearer ".concat(e)}}),toggleUserPrivate:(e,r)=>a.Ay.put("/api/administrator/update-private",{id:e},{headers:{Authorization:"Bearer ".concat(r)}})}},52862:(e,r,t)=>{Promise.resolve().then(t.bind(t,87708)),Promise.resolve().then(t.bind(t,67234))},67234:(e,r,t)=>{"use strict";t.d(r,{default:()=>u});var a=t(95155),s=t(12115),n=t(36268),o=t(11032),l=t(11725),i=t(18617),c=t(38543),d=t(35695),g=t(18579),h=t(52814);function u(){let[e,r]=(0,s.useState)([]),[t,u]=(0,s.useState)(1),[m,p]=(0,s.useState)(1),[x,y]=(0,s.useState)(!1),b=async()=>{y(!0);try{let e=localStorage.getItem("sessionToken")||"",a=await l.A.fetchUsers({page:t,perPage:20},e);if(console.log("Fetch users response:",a),a&&a.payload){let{total:e,users:t}=a.payload;r(t||[]),p(Math.ceil(e/20))}else console.warn("No payload in users response"),r([])}catch(e){console.error("Error fetching users:",e),r([]),c.oR.error("An error occurred while fetching users. Please try again.")}finally{y(!1)}};(0,s.useEffect)(()=>{b()},[t]);let v=async(t,a)=>{try{let s=localStorage.getItem("sessionToken")||"",n=await l.A.deleteUser(t,s);if(n.payload.success){let t=[...e];t.splice(a,1),r(t),c.oR.success("Delete successful!")}else console.error("Error deleting user:",n.payload),c.oR.error("Error deleting user. Please try again.")}catch(e){console.error("Unexpected error:",e),c.oR.error("An error occurred during deletion. Please try again.")}},f=async e=>{let r=e.private?"mở kho\xe1":"kho\xe1";if(confirm("Bạn c\xf3 chắc chắn muốn ".concat(r," t\xe0i khoản ").concat(e.username,"?")))try{let t=localStorage.getItem("sessionToken")||"";(await i.A.toggleUserPrivate(e._id,t)).payload.success?(c.oR.success("".concat(r.charAt(0).toUpperCase()+r.slice(1)," t\xe0i khoản th\xe0nh c\xf4ng")),b()):c.oR.error("Kh\xf4ng thể ".concat(r," t\xe0i khoản"))}catch(e){console.error("Error toggling user private status:",e),c.oR.error("C\xf3 lỗi xảy ra khi ".concat(r," t\xe0i khoản"))}},j=[{accessorKey:"username",header:"T\xean"},{accessorKey:"phonenumber",header:"Số điện thoại"},{accessorKey:"email",header:"Email"},{accessorKey:"department",header:"Ph\xf2ng ban",cell:e=>{let{row:r}=e;return(0,a.jsx)("div",{children:r.original.department?(0,a.jsx)("div",{className:"font-medium text-sm",children:r.original.department.name}):(0,a.jsx)("span",{className:"text-gray-400 italic text-sm",children:"Chưa c\xf3 ph\xf2ng ban"})})}},{accessorKey:"rule",header:"Chức vụ",cell:e=>{let{row:r}=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)(h.eG,{role:r.original.rule}),r.original.departmentRole&&"member"!==r.original.departmentRole&&(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:"manager"===r.original.departmentRole?"Quản l\xfd":r.original.departmentRole})})]})}},{accessorKey:"private",header:"T\xecnh Trạng",cell:e=>{let{row:r}=e;return(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(r.original.private?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:r.original.private?"Đ\xe3 kho\xe1":"Hoạt động"})}},{accessorKey:"createdAt",header:"Ng\xe0y đăng",cell:e=>{let{row:r}=e;return new Date(r.original.createdAt).toLocaleDateString()}},{header:"H\xe0nh động",cell:e=>{let{row:r,rowIndex:t}=e,s=(0,d.useRouter)();return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>{s.push("/dashboard/user/".concat(r.original._id))},className:"px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Chỉnh sửa"}),(0,a.jsx)("button",{onClick:()=>f(r.original),className:"px-3 py-1 text-sm rounded-md ".concat(r.original.private?"bg-green-500 hover:bg-green-600 text-white":"bg-yellow-500 hover:bg-yellow-600 text-white"),children:r.original.private?"Mở kho\xe1":"Kho\xe1"}),(0,a.jsx)("button",{onClick:()=>v(r.original,r.index),className:"px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600",children:"X\xf3a"})]})}}],N=(0,n.N4)({data:e||[],columns:j,getCoreRowModel:(0,o.HT)()});return x?(0,a.jsx)("div",{className:"w-full p-4 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-600",children:"Đang tải dữ liệu..."})}):(0,a.jsx)("div",{className:"w-full p-4",children:0===e.length?(0,a.jsx)("div",{className:"text-center text-gray-600 py-8",children:"Kh\xf4ng c\xf3 dữ liệu người d\xf9ng"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("table",{className:"table-auto w-full border-collapse border border-gray-300 bg-white",children:[(0,a.jsx)("thead",{children:N.getHeaderGroups().map(e=>(0,a.jsx)("tr",{className:"bg-gray-200",children:e.headers.map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-gray-800 font-semibold",children:(0,n.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)("tbody",{children:N.getRowModel().rows.map(e=>(0,a.jsx)("tr",{className:"border border-gray-300 hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-2 text-gray-900",children:(0,n.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),(0,a.jsx)(g.A,{currentPage:t,totalPages:m,onPageChange:e=>u(e)})]})})}}},e=>{e.O(0,[9268,3235,8543,6268,2779,8441,5964,7358],()=>e(e.s=52862)),_N_E=e.O()}]);