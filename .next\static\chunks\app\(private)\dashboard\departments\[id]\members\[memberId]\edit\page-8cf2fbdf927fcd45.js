(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2659],{1480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var n=r(95155),s=r(12115),a=r(35695),i=r(38543),l=r(3136),o=r(11725),c=r(87708),d=r(9424),h=r(44494),m=r(38637),u=r.n(m);function p(){return(p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var g=(0,s.forwardRef)(function(e,t){var r=e.color,n=e.size,a=void 0===n?24:n,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return s.createElement("svg",p({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),s.createElement("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),s.createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"}))});function x(){return(x=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}g.propTypes={color:u().string,size:u().oneOfType([u().string,u().number])},g.displayName="AlertTriangle";var b=(0,s.forwardRef)(function(e,t){var r=e.color,n=e.size,a=void 0===n?24:n,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return s.createElement("svg",x({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("path",{d:"M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"}))});b.propTypes={color:u().string,size:u().oneOfType([u().string,u().number])},b.displayName="Key";var y=r(46114),v=r(11080),f=r(52814);function j(){let e=(0,a.useRouter)(),t=(0,a.useParams)(),r=t.id,m=t.memberId,[u,p]=(0,s.useState)(!1),[x,j]=(0,s.useState)(!0),[N,w]=(0,s.useState)([]),[k,O]=(0,s.useState)(null),[E,z]=(0,s.useState)({permissions:[],departmentRole:"member"}),[R,A]=(0,s.useState)(!1),[T,C]=(0,s.useState)(!1),[S,P]=(0,s.useState)(""),[L,q]=(0,s.useState)(!1),[B,_]=(0,s.useState)(!1);(0,s.useEffect)(()=>{r&&m&&(I(),Q())},[r,m]);let I=async()=>{try{let t=localStorage.getItem("sessionToken")||"",n=await l.A.getDepartmentMembers(r,{page:1,perPage:100},t);if(n.payload.success){let t=n.payload.members.find(e=>e._id===m);t?(O(t),z({permissions:t.permissions||[],departmentRole:t.departmentRole||"member"})):(i.oR.error("Kh\xf4ng t\xecm thấy th\xe0nh vi\xean"),e.push("/dashboard/departments/".concat(r)))}else i.oR.error("Kh\xf4ng thể tải th\xf4ng tin th\xe0nh vi\xean"),e.push("/dashboard/departments/".concat(r))}catch(t){console.error("Error fetching member:",t),i.oR.error("Lỗi khi tải th\xf4ng tin th\xe0nh vi\xean"),e.push("/dashboard/departments/".concat(r))}finally{j(!1)}},Q=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await l.A.getAvailablePermissions(e);t.payload.success&&w(t.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},U=async t=>{t.preventDefault();try{p(!0);let t=localStorage.getItem("sessionToken")||"",n=await l.A.updateMemberPermissions(r,m,E,t);n.payload.success?(i.oR.success("Cập nhật quyền th\xe0nh vi\xean th\xe0nh c\xf4ng"),e.push("/dashboard/departments/".concat(r))):i.oR.error(n.payload.message||"Kh\xf4ng thể cập nhật quyền th\xe0nh vi\xean")}catch(e){console.error("Error updating member permissions:",e),i.oR.error("Lỗi khi cập nhật quyền th\xe0nh vi\xean")}finally{p(!1)}},M=async()=>{try{q(!0);let t=localStorage.getItem("sessionToken")||"",n=await l.A.removeMemberFromDepartment(r,m,t);n.payload.success?(i.oR.success("X\xf3a th\xe0nh vi\xean khỏi ph\xf2ng ban th\xe0nh c\xf4ng"),e.push("/dashboard/departments/".concat(r))):i.oR.error(n.payload.message||"Kh\xf4ng thể x\xf3a th\xe0nh vi\xean")}catch(e){console.error("Error deleting member:",e),i.oR.error("Lỗi khi x\xf3a th\xe0nh vi\xean")}finally{q(!1),A(!1)}},K=async()=>{if(!S.trim())return void i.oR.error("Vui l\xf2ng nhập mật khẩu mới");try{_(!0);let e=localStorage.getItem("sessionToken")||"",t=await o.A.updateUser({userId:m,password:S},e);t.payload.success?(i.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng"),C(!1),P("")):i.oR.error(t.payload.message||"Kh\xf4ng thể đổi mật khẩu")}catch(e){console.error("Error resetting password:",e),i.oR.error("Lỗi khi đổi mật khẩu")}finally{_(!1)}},H=N.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});return x?(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):k?(0,n.jsx)(c.default,{requiredPermission:"admin",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,n.jsx)("button",{onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(d.A,{size:20})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa quyền th\xe0nh vi\xean"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Cập nhật quyền hạn cho th\xe0nh vi\xean ph\xf2ng ban"})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,n.jsx)(h.A,{size:20}),"Th\xf4ng tin th\xe0nh vi\xean"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xean người d\xf9ng"}),(0,n.jsx)("p",{className:"text-gray-900 font-medium",children:k.username})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,n.jsx)("p",{className:"text-gray-900",children:k.email})]}),k.phonenumber&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),(0,n.jsx)("p",{className:"text-gray-900",children:k.phonenumber})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vai tr\xf2 hiện tại"}),(0,n.jsx)(f.Ex,{variant:"info",children:"manager"===k.departmentRole?"Quản l\xfd ph\xf2ng ban":"Th\xe0nh vi\xean"})]})]})]}),(0,n.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,n.jsx)("input",{type:"radio",name:"departmentRole",value:"member",checked:"member"===E.departmentRole,onChange:e=>z(t=>({...t,departmentRole:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Th\xe0nh vi\xean"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Th\xe0nh vi\xean thường của ph\xf2ng ban"})]})]}),(0,n.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,n.jsx)("input",{type:"radio",name:"departmentRole",value:"manager",checked:"manager"===E.departmentRole,onChange:e=>z(t=>({...t,departmentRole:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["C\xf3 quyền quản l\xfd th\xe0nh vi\xean trong ph\xf2ng ban","department_manager"===k.rule&&"manager"===E.departmentRole&&(0,n.jsx)("span",{className:"text-green-600 ml-1",children:"(Đang l\xe0 quản l\xfd)"}),"department_manager"===k.rule&&"manager"!==E.departmentRole&&(0,n.jsx)("span",{className:"text-orange-600 ml-1",children:"(Sẽ bị hạ cấp)"})]})]})]})]}),"department_manager"===k.rule&&"manager"!==E.departmentRole&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)(g,{size:20,className:"text-orange-500 mt-0.5 mr-3 flex-shrink-0"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-orange-800 mb-1",children:"Cảnh b\xe1o thay đổi vai tr\xf2"}),(0,n.jsxs)("p",{className:"text-sm text-orange-700",children:["Th\xe0nh vi\xean n\xe0y hiện đang l\xe0 ",(0,n.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),'. Nếu thay đổi về "Th\xe0nh vi\xean", họ sẽ mất quyền quản l\xfd ph\xf2ng ban v\xe0 vai tr\xf2 hệ thống sẽ được thay đổi về "Th\xe0nh vi\xean ph\xf2ng ban".']})]})]})}),"manager"===E.departmentRole&&"department_manager"!==k.rule&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)(g,{size:20,className:"text-blue-500 mt-0.5 mr-3 flex-shrink-0"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:"Th\xf4ng b\xe1o thăng cấp"}),(0,n.jsxs)("p",{className:"text-sm text-blue-700",children:["Th\xe0nh vi\xean n\xe0y sẽ được thăng cấp th\xe0nh ",(0,n.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),". Nếu ph\xf2ng ban đ\xe3 c\xf3 quản l\xfd kh\xe1c, họ sẽ tự động bị hạ cấp về th\xe0nh vi\xean."]})]})]})})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền hạn cụ thể"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Chọn c\xe1c quyền cụ thể cho th\xe0nh vi\xean n\xe0y (bổ sung v\xe0o quyền mặc định của ph\xf2ng ban)"}),(0,n.jsx)("div",{className:"space-y-4",children:Object.entries(H).map(e=>{let[t,r]=e;return(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:t}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:r.map(e=>(0,n.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:E.permissions.includes(e.key),onChange:t=>{var r,n;return r=e.key,n=t.target.checked,void z(e=>({...e,permissions:n?[...e.permissions,r]:e.permissions.filter(e=>e!==r)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},t)})}),(0,n.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,n.jsx)("strong",{children:"Lưu \xfd:"})," Th\xe0nh vi\xean sẽ c\xf3 tổng cộng ",E.permissions.length," quyền được chọn cộng với c\xe1c quyền mặc định của ph\xf2ng ban."]})})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,n.jsx)(g,{size:20,className:"text-orange-500"}),"Quản l\xfd t\xe0i khoản"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"border border-blue-200 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,n.jsx)(b,{size:16,className:"text-blue-500"}),"Đổi mật khẩu"]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Đặt lại mật khẩu cho th\xe0nh vi\xean n\xe0y"}),T?(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("input",{type:"password",value:S,onChange:e=>P(e.target.value),placeholder:"Nhập mật khẩu mới",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)("button",{type:"button",onClick:K,disabled:B,className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:B?"Đang cập nhật...":"Cập nhật"}),(0,n.jsx)("button",{type:"button",onClick:()=>{C(!1),P("")},className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,n.jsx)("button",{type:"button",onClick:()=>C(!0),className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:"Đổi mật khẩu"})]}),(0,n.jsxs)("div",{className:"border border-red-200 rounded-lg p-4",children:[(0,n.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,n.jsx)(y.A,{size:16,className:"text-red-500"}),"X\xf3a khỏi ph\xf2ng ban"]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"X\xf3a th\xe0nh vi\xean n\xe0y khỏi ph\xf2ng ban (kh\xf4ng x\xf3a t\xe0i khoản)"}),R?(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("div",{className:"p-3 bg-red-50 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-red-800",children:[(0,n.jsx)("strong",{children:"Cảnh b\xe1o:"})," Th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban v\xe0 mất tất cả quyền li\xean quan. T\xe0i khoản vẫn tồn tại nhưng kh\xf4ng thuộc ph\xf2ng ban n\xe0o."]})}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)("button",{type:"button",onClick:M,disabled:L,className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:L?"Đang x\xf3a...":"X\xe1c nhận x\xf3a"}),(0,n.jsx)("button",{type:"button",onClick:()=>A(!1),className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,n.jsx)("button",{type:"button",onClick:()=>A(!0),className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors",children:"X\xf3a khỏi ph\xf2ng ban"})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)("button",{type:"button",onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsxs)("button",{type:"submit",disabled:u,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,n.jsx)(v.A,{size:16}),u?"Đang cập nhật...":"Cập nhật quyền"]})]})]})]})}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy th\xe0nh vi\xean"})})}},11080:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115),s=r(38637),a=r.n(s);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),n.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),n.createElement("polyline",{points:"7 3 7 8 15 8"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Save";let o=l},11725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(27937);let s={fetchUsers:(e,t)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>n.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>n.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>n.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>n.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>n.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>n.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},44494:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115),s=r(38637),a=r.n(s);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),n.createElement("circle",{cx:"12",cy:"7",r:"4"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="User";let o=l},46114:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115),s=r(38637),a=r.n(s);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("polyline",{points:"3 6 5 6 21 6"}),n.createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),n.createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),n.createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Trash2";let o=l},52814:(e,t,r)=>{"use strict";r.d(t,{Ex:()=>s,eG:()=>a});var n=r(95155);r(12115);let s=e=>{let{children:t,variant:r="default",size:s="md",className:a="",dot:i=!1}=e;return(0,n.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[r],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[s],"\n        ").concat(a,"\n      "),children:[i&&(0,n.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[r])}),t]})},a=e=>{let{role:t,className:r=""}=e,a={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},i=a[t]||a.user;return(0,n.jsx)(s,{variant:i.variant,className:r,children:i.label})}},86888:(e,t,r)=>{Promise.resolve().then(r.bind(r,1480))}},e=>{e.O(0,[9268,3235,8543,3998,8441,5964,7358],()=>e(e.s=86888)),_N_E=e.O()}]);